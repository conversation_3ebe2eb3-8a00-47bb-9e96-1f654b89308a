/**
 * Verification script for Enhanced Storage Service and Login Compatibility Service
 * This can be run in the browser console to test the fixes
 */

// Test Enhanced Storage Service functionality
export const testEnhancedStorage = () => {
  console.log('🧪 Testing Enhanced Storage Service...');
  
  const testData = {
    email: '<EMAIL>',
    id_store: '123',
    name: 'Test User',
    active: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    role: 'user'
  };

  // Test 1: Storage method testing
  console.log('📝 Test 1: Storage method availability');
  
  const testLocalStorage = () => {
    try {
      localStorage.setItem('test_enhanced', 'test');
      const retrieved = localStorage.getItem('test_enhanced');
      localStorage.removeItem('test_enhanced');
      return retrieved === 'test';
    } catch {
      return false;
    }
  };

  const testSessionStorage = () => {
    try {
      sessionStorage.setItem('test_enhanced', 'test');
      const retrieved = sessionStorage.getItem('test_enhanced');
      sessionStorage.removeItem('test_enhanced');
      return retrieved === 'test';
    } catch {
      return false;
    }
  };

  const testCookies = () => {
    try {
      document.cookie = 'test_enhanced=1; SameSite=Lax';
      const hasCookie = document.cookie.includes('test_enhanced=1');
      document.cookie = 'test_enhanced=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
      return hasCookie;
    } catch {
      return false;
    }
  };

  const storageResults = {
    localStorage: testLocalStorage(),
    sessionStorage: testSessionStorage(),
    cookies: testCookies()
  };

  console.log('Storage availability:', storageResults);

  // Test 2: Data storage and retrieval
  console.log('📝 Test 2: Data storage and retrieval');
  
  const testDataString = JSON.stringify(testData);
  
  // Try localStorage
  if (storageResults.localStorage) {
    try {
      localStorage.setItem('user_test', testDataString);
      const retrieved = localStorage.getItem('user_test');
      const parsed = JSON.parse(retrieved || '{}');
      localStorage.removeItem('user_test');
      
      console.log('✅ localStorage: Data stored and retrieved successfully');
      console.log('Retrieved data matches:', JSON.stringify(parsed) === testDataString);
    } catch (error) {
      console.log('❌ localStorage: Failed -', error);
    }
  }

  // Try sessionStorage
  if (storageResults.sessionStorage) {
    try {
      sessionStorage.setItem('user_test', testDataString);
      const retrieved = sessionStorage.getItem('user_test');
      const parsed = JSON.parse(retrieved || '{}');
      sessionStorage.removeItem('user_test');
      
      console.log('✅ sessionStorage: Data stored and retrieved successfully');
      console.log('Retrieved data matches:', JSON.stringify(parsed) === testDataString);
    } catch (error) {
      console.log('❌ sessionStorage: Failed -', error);
    }
  }

  // Try cookies
  if (storageResults.cookies) {
    try {
      const encodedData = encodeURIComponent(testDataString);
      document.cookie = `user_test=${encodedData}; path=/; SameSite=Lax; Secure`;
      
      const cookies = document.cookie.split(';');
      const userCookie = cookies.find(cookie => cookie.trim().startsWith('user_test='));
      
      if (userCookie) {
        const retrievedEncoded = userCookie.split('=')[1];
        const retrievedData = decodeURIComponent(retrievedEncoded);
        const parsed = JSON.parse(retrievedData);
        
        // Clean up
        document.cookie = 'user_test=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
        
        console.log('✅ Cookies: Data stored and retrieved successfully');
        console.log('Retrieved data matches:', JSON.stringify(parsed) === testDataString);
      } else {
        console.log('❌ Cookies: Failed to store data');
      }
    } catch (error) {
      console.log('❌ Cookies: Failed -', error);
    }
  }

  return storageResults;
};

// Test Login Compatibility Service functionality
export const testLoginCompatibility = () => {
  console.log('🧪 Testing Login Compatibility Service...');
  
  // Test 1: Browser detection
  console.log('📝 Test 1: Browser detection');
  
  const userAgent = navigator.userAgent.toLowerCase();
  const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
  const isFirefox = userAgent.includes('firefox');
  const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
  const isEdge = userAgent.includes('edge');
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

  const browserInfo = {
    browser: isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : isEdge ? 'Edge' : 'Unknown',
    isMobile,
    userAgent: navigator.userAgent,
    cookiesEnabled: navigator.cookieEnabled,
    isSecureContext: window.isSecureContext,
    protocol: window.location.protocol
  };

  console.log('Browser info:', browserInfo);

  // Test 2: Compatibility issues detection
  console.log('📝 Test 2: Compatibility issues detection');
  
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  const storageSupport = testEnhancedStorage();
  
  if (!storageSupport.localStorage && !storageSupport.sessionStorage && !storageSupport.cookies) {
    issues.push('No storage methods available - login will not persist');
    recommendations.push('Enable cookies in your browser settings');
  }

  if (!browserInfo.cookiesEnabled) {
    issues.push('Cookies are disabled');
    recommendations.push('Enable cookies for this site');
  }

  if (browserInfo.protocol !== 'https:' && !window.location.hostname.includes('localhost')) {
    issues.push('Not using HTTPS - some features may not work');
    recommendations.push('Access the site using HTTPS');
  }

  // Browser-specific checks
  if (browserInfo.browser === 'Safari' && browserInfo.isMobile) {
    if (!storageSupport.localStorage) {
      issues.push('Safari private browsing detected - storage limited');
      recommendations.push('Exit private browsing mode for full functionality');
    }
  }

  if (browserInfo.browser === 'Firefox') {
    if (!storageSupport.cookies) {
      issues.push('Firefox Enhanced Tracking Protection may be blocking storage');
      recommendations.push('Add this site to Firefox exceptions or use Standard protection');
    }
  }

  const overall = issues.length === 0 ? 'good' : 
                 issues.some(issue => issue.includes('No storage methods')) ? 'error' : 'warning';

  const compatibilityResult = {
    overall,
    issues,
    recommendations,
    storageSupport,
    browserInfo
  };

  console.log('Compatibility check result:', compatibilityResult);

  // Test 3: Summary
  console.log('📝 Test 3: Summary');
  
  if (overall === 'good') {
    console.log('✅ All compatibility checks passed! Normal login should work perfectly.');
  } else if (overall === 'warning') {
    console.log('⚠️ Some compatibility warnings detected. Normal login should work but may have minor issues.');
    console.log('Issues:', issues);
    console.log('Recommendations:', recommendations);
  } else {
    console.log('❌ Critical compatibility issues detected. Normal login may fail.');
    console.log('Issues:', issues);
    console.log('Recommendations:', recommendations);
  }

  return compatibilityResult;
};

// Main verification function
export const verifyStorageFixes = () => {
  console.log('🚀 Verifying Enhanced Storage and Login Compatibility Fixes');
  console.log('='.repeat(60));
  
  const storageResults = testEnhancedStorage();
  console.log('\n' + '='.repeat(60));
  
  const compatibilityResults = testLoginCompatibility();
  console.log('\n' + '='.repeat(60));
  
  console.log('🎯 Summary of Fixes Implemented:');
  console.log('1. ✅ Enhanced Storage Service with verification and fallback chain');
  console.log('2. ✅ Browser Compatibility Diagnostics for normal login');
  console.log('3. ✅ Improved error handling and logging');
  console.log('4. ✅ Storage method testing and validation');
  
  return {
    storage: storageResults,
    compatibility: compatibilityResults
  };
};

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as typeof window & { verifyStorageFixes?: typeof verifyStorageFixes }).verifyStorageFixes = verifyStorageFixes;
  (window as typeof window & { testEnhancedStorage?: typeof testEnhancedStorage }).testEnhancedStorage = testEnhancedStorage;
  (window as typeof window & { testLoginCompatibility?: typeof testLoginCompatibility }).testLoginCompatibility = testLoginCompatibility;
}
