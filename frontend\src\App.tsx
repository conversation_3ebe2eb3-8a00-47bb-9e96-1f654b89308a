import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useAuth } from './contexts/AuthContext';
import { Box, TextField, Typography, CircularProgress, IconButton, Drawer, List, ListItem, ListItemText, ListItemIcon, Button, Tooltip, Menu, MenuItem, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';
import ChatIcon from '@mui/icons-material/Chat';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import EditIcon from '@mui/icons-material/Edit';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import SaveIcon from '@mui/icons-material/Save';
import './App.css';
import logo from './assets/logo.png';
import NavigationWrapper from './components/common/NavigationWrapper';
import PageContainer from './components/common/PageContainer';
import ErrorBoundary from './components/ErrorBoundary';
import CSRFErrorBoundary from './components/common/CSRFErrorBoundary';
import { ScrollToTop } from './components/ScrollToTop';
import { API_URL } from './config/api';
import { fetchWithDeduplication, invalidateCache } from './services/apiService';
import { useNavigate } from 'react-router-dom';
import { ChatInputWrapper } from './components/meta/ChatInputWrapper';
import { initMetaSDK } from './services/init';
import { MetaPermissionKey } from './services/types';
import LoadingIndicator from './components/common/LoadingIndicator';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useFeedback } from './contexts/feedback.context';
import { useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import CookieBanner from './components/common/CookieBanner';
import authService from './services/authService';
import consoleFilter from './utils/consoleFilter';
import { logger } from './utils/logger';
import { storeService } from './services/storeService';

// ----------------------------------------
// Lightweight debounce helper (no lodash)
// ----------------------------------------
const debounceFn = <T extends (...args: unknown[]) => void>(func: T, wait = 300) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};
// --- BEGIN DATE HELPER FUNCTIONS ---
const isSameDay = (d1: Date, d2: Date): boolean => {
  // Check if dates are valid before comparing
  if (!d1 || !d2 || isNaN(d1.getTime()) || isNaN(d2.getTime())) {
    return false;
  }
  return d1.getFullYear() === d2.getFullYear() &&
         d1.getMonth() === d2.getMonth() &&
         d1.getDate() === d2.getDate();
};

const isToday = (date: Date): boolean => {
  if (!date || isNaN(date.getTime())) return false;
  const today = new Date();
  return isSameDay(date, today);
};

const isYesterday = (date: Date): boolean => {
  if (!date || isNaN(date.getTime())) return false;
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return isSameDay(date, yesterday);
};

// Calculates the difference in days, ignoring time component
const differenceInDays = (date1: Date, date2: Date): number => {
   if (!date1 || !date2 || isNaN(date1.getTime()) || isNaN(date2.getTime())) {
     return Infinity; // Or handle error appropriately
   }
   // Use UTC dates to avoid timezone issues affecting day calculation
   const day1UTC = Date.UTC(date1.getFullYear(), date1.getMonth(), date1.getDate());
   const day2UTC = Date.UTC(date2.getFullYear(), date2.getMonth(), date2.getDate());
   const diffTime = Math.abs(day2UTC - day1UTC);
   return Math.floor(diffTime / (1000 * 60 * 60 * 24)); // Use floor for whole days difference
}

// Function to determine the group label for a chat date
const getChatGroup = (chatDate: Date, t: TFunction): string => {
    if (!chatDate || isNaN(chatDate.getTime())) return t('chat.dateGroup.invalid'); // Handle invalid dates
    const today = new Date();
    if (isToday(chatDate)) return t('chat.dateGroup.today');
    if (isYesterday(chatDate)) return t('chat.dateGroup.yesterday');
    const daysDiff = differenceInDays(today, chatDate);
    if (daysDiff <= 7) return t('chat.dateGroup.previous7Days');
    if (daysDiff <= 30) return t('chat.dateGroup.previous30Days');
    // Optional: Add more specific groups like Month Year
    // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    // return `${monthNames[chatDate.getMonth()]} ${chatDate.getFullYear()}`;
    return t('chat.dateGroup.older');
};
// --- END DATE HELPER FUNCTIONS ---

interface Message {
  role: string;
  content: string;
  timestamp?: Date;
  contextDocuments?: ContextDocument[];
}

interface Chat {
  conversation_id: string;
  title: string;
  full_title?: string;
  messages: Message[];
  timestamp: Date;
  updated_at: Date;
  full_user_prompt?: string;
}

// Add interfaces for API responses
interface ChatHistory {
  id?: string;
  _id?: string;
  conversation_id?: string;
  title: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  messages?: ChatMessage[];
  full_user_prompt?: string;
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  created_at: string;
  chat_id: string;
}

// Update the interface for chat API response
interface ChatResponse {
  message?: string;
  response?: string;  // Add this field as an alternative to message
  conversation_id: string;
  title?: string;
  full_title?: string;
}

// Add this interface for context documents
// --- BEGIN NEW CONTENT INTERFACES ---
interface ProductDetailsContent {
  products?: Array<{ name: string; price: number; stock?: number | string }>; // Allow string for stock as sometimes it might be textual
  product_count?: number;
}

interface StoreCustomersContent {
  customers?: Array<{ name: string; order_count?: number; total_spent?: number }>;
  total_customers?: number;
  average_spend_per_customer?: number;
}

interface ActiveStoresContent {
  name?: string;
  business_type?: string;
  country?: { name?: string }; // Nested object for country
  social_media?: Record<string, string>; // Object for social media links
}

// --- BEGIN SPECIFIC DOCUMENT INTERFACES ---
interface BaseContextDocument {
  id: string;
  similarity: number;
}

interface ProductContextDocument extends BaseContextDocument {
  type: 'product_details'; // Literal type
  content: ProductDetailsContent;
}

interface CustomerContextDocument extends BaseContextDocument {
  type: 'store_customers'; // Literal type
  content: StoreCustomersContent;
}

interface StoreContextDocument extends BaseContextDocument {
  type: 'active_stores'; // Literal type
  content: ActiveStoresContent;
}

// Interface for any other document type we don't have specific content for yet
interface UnknownContextDocument extends BaseContextDocument {
  type: string; // Fallback for other types
  content: Record<string, unknown>; // Keep original broad type for fallbacks
}

// Replace the old interface with this type union:
type ContextDocument = ProductContextDocument | CustomerContextDocument | StoreContextDocument | UnknownContextDocument;

// Add type-guard helper functions
const isProductDocument = (doc: ContextDocument): doc is ProductContextDocument => {
  return doc.type === 'product_details';
};

const isCustomerDocument = (doc: ContextDocument): doc is CustomerContextDocument => {
  return doc.type === 'store_customers';
};

const isStoreDocument = (doc: ContextDocument): doc is StoreContextDocument => {
  return doc.type === 'active_stores';
};

// Add a validation helper
const isValidMetaPermissionKey = (key: string): key is MetaPermissionKey => {
  return [
    'instagram_basic',
    'instagram_manage_insights', 
    'instagram_manage_comments',
    'instagram_branded_content_brand',
    'pages_read_engagement',
    'pages_read_user_content',
    'ads_read',
    'ads_management'
  ].includes(key);
};

// Add declaration for global window object
declare global {
  interface Window {
    isVerticalNav?: boolean;
    isChatPage?: boolean;
    addEventListener(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | AddEventListenerOptions
    ): void;
    removeEventListener(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | EventListenerOptions
    ): void;
  }
}

const DRAWER_WIDTH = 300;

// Update the ExtendedChatResponse interface to include context_documents
interface ExtendedChatResponse extends ChatResponse {
  meta_permission_error?: boolean;
  missing_permissions?: string[]; // This remains string[] from the API
  full_user_prompt?: string;
  context_documents?: ContextDocument[];
}

const App = () => {
  const theme = useTheme();
  const { user, viewingStoreID } = useAuth();
  const { openFeedbackModal } = useFeedback();
  const { t, i18n } = useTranslation();
  const [credits, setCredits] = useState<number | null>(null);
  
  // Determine the store ID to use for chat operations
  // Fallback to user's own store ID only if viewingStoreID is not set (e.g., not on a specific store page)
  const chatStoreID = viewingStoreID ?? user?.id_store?.toString();
  
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedChatId, setSelectedChatId] = useState<string>('');
  const [renameDialogOpen, setRenameDialogOpen] = useState(false);
  const [newTitle, setNewTitle] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const sdkInitialized = React.useRef(false);
  const [lastResponse, setLastResponse] = useState<{
    meta_permission_error?: boolean;
    missing_permissions?: MetaPermissionKey[]; // Update to use type
    response?: string;
    full_user_prompt?: string;
  }>({});

  // States for enhanced loading indicator
  const [loadingMode, setLoadingMode] = useState<string | null>(null);
  const [elapsedTime, setElapsedTime] = useState<number>(0);
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0);
  const [timerIntervalId, setTimerIntervalId] = useState<NodeJS.Timeout | null>(null);
  const [stepIntervalId, setStepIntervalId] = useState<NodeJS.Timeout | null>(null);

  // Add state for feedback interaction tracking
  const [interactionCount, setInteractionCount] = useState(0);
  const [feedbackModalShownThisSession, setFeedbackModalShownThisSession] = useState(false);

  // Define steps for different modes using useMemo
  const STEPS = useMemo(() => ({
    Think: [t('chat.loadingSteps.thinking'), t('chat.loadingSteps.analyzingContext'), t('chat.loadingSteps.generating')],
    DeepSearch: [t('chat.loadingSteps.processingQuery'), t('chat.loadingSteps.searchingBrave'), t('chat.loadingSteps.analyzingResults'), t('chat.loadingSteps.generating')],
    DeeperSearch: [t('chat.loadingSteps.processingQuery'), t('chat.loadingSteps.searchingGoogle'), t('chat.loadingSteps.analyzingResults'), t('chat.loadingSteps.generating')],
    Default: [t('chat.loadingSteps.processing'), t('chat.loadingSteps.generating')] // Fallback/standard mode
  }), [t]);

  const STEP_INTERVAL_MS = 4000; // Cycle step text every 4 seconds

  // Effect for timer
  useEffect(() => {
    if (isLoading && timerIntervalId === null) {
      const intervalId = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
      setTimerIntervalId(intervalId);
    } else if (!isLoading && timerIntervalId !== null) {
      clearInterval(timerIntervalId);
      setTimerIntervalId(null);
    }

    // Cleanup function
    return () => {
      if (timerIntervalId) {
        clearInterval(timerIntervalId);
      }
    };
  }, [isLoading, timerIntervalId]);

  // Effect for cycling steps
  useEffect(() => {
    if (isLoading && loadingMode && stepIntervalId === null) {
      const stepsForMode = STEPS[loadingMode as keyof typeof STEPS] || STEPS.Default;
      const intervalId = setInterval(() => {
        setCurrentStepIndex(prev => (prev + 1) % stepsForMode.length);
      }, STEP_INTERVAL_MS);
      setStepIntervalId(intervalId);
    } else if (!isLoading && stepIntervalId !== null) {
      clearInterval(stepIntervalId);
      setStepIntervalId(null);
    }

    // Cleanup function
    return () => {
      if (stepIntervalId) {
        clearInterval(stepIntervalId);
      }
    };
  }, [isLoading, loadingMode, stepIntervalId, STEPS]);

  const messagesEndRef = useRef<HTMLDivElement | null>(null);

  // Debounced scroll to bottom to avoid oscillation during rapid resize/re-renders
  useEffect(() => {
    const handler = debounceFn(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 300);

    handler();

    // No dependencies other than messages length; debounceFn recreated each render but lightweight
    return () => {
      // No cleanup necessary for simple timeout debounce
    };
  }, [currentChat?.messages.length]);

  const generateTempId = () => `temp-${Date.now()}-${Math.random().toString(36).slice(2, 11)}`;

  // Update the createNewChat function to use a temporary ID for new chats
  // AND reset feedback interaction state
  const createNewChat = useCallback(() => {
    const newChat: Chat = {
      conversation_id: generateTempId(), // Use a temporary unique ID
      title: t('chat.newChatTitle'),
      full_title: t('chat.newChatTitle'),
      messages: [{
        role: 'assistant',
        content: t('chat.initialAssistantMessage'), // Use translation key
        timestamp: new Date()
      }],
      timestamp: new Date(),
      updated_at: new Date(),
      full_user_prompt: undefined
    };
    setCurrentChat(newChat);
    // Add the new temporary chat to the main list immediately
    setChats(prevChats => [newChat, ...prevChats].sort((a, b) => b.updated_at.getTime() - a.updated_at.getTime()));
    // Reset feedback state on new chat
    setInteractionCount(0);
    setFeedbackModalShownThisSession(false);
  }, [t]);

  // Handle navigation to Meta Permissions
  const handleNavigateToMetaPermissions = () => {
    // Navigate to Meta Dashboard with permissions tab selected
    navigate('/meta-dashboard');
    
    // Give time for the navigation to happen, then click the permissions tab
    setTimeout(() => {
      const permissionsTabButton = document.querySelector('button[role="tab"]:nth-child(8)') as HTMLButtonElement;
      if (permissionsTabButton) {
        permissionsTabButton.click();
      }
    }, 300);
  };

  // Use useCallback to memoize the fetchChatHistory function
  const fetchChatHistory = useCallback(async () => {
    setLoading(true); // Checklist Step 2: Set loading true at start
    try {
      if (!chatStoreID) { 
        logger.error("Cannot fetch chat history: Store ID is missing."); 
        setLoading(false); 
        return; 
      }
      
      const token = authService.getToken();
      
      // Use fetchWithDeduplication instead of direct axios call
      const data = await fetchWithDeduplication<ChatHistory[]>(`${API_URL}/api/chat/history/${chatStoreID}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      // Checklist Step 3: Update chats state with fetched data
      if (data && data.length > 0) {
        logger.debug("Raw chat history from MongoDB:", data); // Keep existing log
        
        const formattedChats = data.map((chat: ChatHistory) => {
          const mongoId = chat._id;
          const conversationId = chat.conversation_id;
          const chatId = conversationId || mongoId || generateTempId();
          
          // Convert message timestamps
          const formattedMessages = (chat.messages || []).map(msg => ({
            ...msg,
            timestamp: new Date(msg.created_at) // Convert string date to Date object
          }));
          
          logger.debug("Processing chat from MongoDB:", { // Keep existing log
            chatId,
            title: chat.title,
            conversation_id: conversationId,
            mongoId,
            full_user_prompt: chat.full_user_prompt
          });

          return {
            conversation_id: chatId,
            title: chat.title || t('chat.untitledChat'),
            messages: formattedMessages, // Use formatted messages
            timestamp: new Date(chat.created_at), // Convert string date to Date object
            updated_at: new Date(chat.updated_at), // Convert string date to Date object
            full_user_prompt: chat.full_user_prompt
          } as Chat; // Ensure type assertion
        }).sort((a, b) => b.updated_at.getTime() - a.updated_at.getTime()); // Sort by updated_at descending

        setChats(formattedChats); // Update the state
      } else {
        setChats([]); // Set to empty array if no history data
      }

    } catch (error) {
      logger.error('Error fetching chat history:', error);
      setChats([]); // Clear chats on error to avoid inconsistent state
    } finally {
        setLoading(false); // Checklist Step 5: Set loading false in finally block
    }
    // Checklist Step 6: Add dependencies to useCallback array
  }, [chatStoreID, setChats, setLoading, t]); // Updated dependency from user?.id_store to chatStoreID, added t

  // Add effect to handle null currentChat state: default to most recent or create new
  useEffect(() => {
    // Only run if history is loaded (loading is false), no chat is active, and user is logged in.
    if (!loading && !currentChat && user) {
      if (chats.length > 0) {
        // If history exists, set the most recent chat as current
        logger.debug("No current chat, setting to most recent from history.");
        setCurrentChat(chats[0]);
      } else {
        // If history is empty, create a new chat
        logger.debug("No current chat and no history, creating a new one.");
        createNewChat();
      }
    }
    // Dependencies: loading state, currentChat state, chats list, user, and createNewChat function
  }, [loading, currentChat, chats, user, createNewChat]);

  // Fetch chat history on component mount and when user changes
  useEffect(() => {
    if (chatStoreID) {
      fetchChatHistory();
    }
  }, [chatStoreID, fetchChatHistory]); // Update dependencies to include chatStoreID

  // Fetch credits when store changes or after chat actions
  useEffect(() => {
    const fetchCredits = async () => {
      if (!chatStoreID) return;
      try {
        const balance = await storeService.getStoreCredits(chatStoreID);
        setCredits(balance);
      } catch (err) {
        console.error('Error fetching credits', err);
      }
    };
    fetchCredits();
  }, [chatStoreID]);

  // Enhanced mouse tracking for drawer with window size monitoring and buffer zone
  useEffect(() => {
    // Drawer width plus buffer to prevent closing when near the edge
    const effectiveDrawerWidth = DRAWER_WIDTH + 20; // Add 20px buffer
    
    // Track current threshold value for opening the drawer
    let openThreshold = Math.min(window.innerWidth * 0.3, 300); // Increased from 20% to 30%
    
    // Minimum threshold to ensure sidebar is accessible on small screens
    const MIN_THRESHOLD = 75; // Increased from 50
    
    // Calculate optimal threshold based on window size
    const calculateThreshold = () => {
      // Use smaller percentage for smaller screens
      const percentage = window.innerWidth < 768 ? 0.2 : 0.3; // Increased from 0.15/0.2 to 0.2/0.3
      // Set max threshold value to prevent sidebar from being too easy to trigger on large screens
      const maxThreshold = Math.min(window.innerWidth * percentage, 300); // Increased from 200 to 300
      // Ensure threshold never goes below minimum value
      openThreshold = Math.max(maxThreshold, MIN_THRESHOLD);
    };
    
    // Update threshold on window resize
    const handleResize = () => {
      calculateThreshold();
    };
    
    // Handle mouse movement with improved logic
    const handleMouseMove = (e: MouseEvent) => {
      // Don't close the drawer if the menu is open
      const isMenuOpen = Boolean(anchorEl);
      
      if (drawerOpen) {
        // When drawer is open, only close it if mouse moves significantly outside drawer area
        // AND the menu isn't open
        if (e.clientX > effectiveDrawerWidth && !isMenuOpen) {
          setDrawerOpen(false);
        }
      } else {
        // When drawer is closed, open it if mouse enters the left edge threshold
        if (e.clientX <= openThreshold) {
          setDrawerOpen(true);
        }
      }
    };

    // Calculate initial threshold
    calculateThreshold();
    
    // Set up event listeners
    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('resize', handleResize);
    
    // Clean up event listeners
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('resize', handleResize);
    };
  }, [drawerOpen, anchorEl]); // Add anchorEl as dependency to react to menu open/close

  // Memoize with useCallback to safely use in dependencies if needed
  const handleSendMessage = useCallback(async (message: string, mode: string | null = null, image: File | null = null) => {
    if (!user || !chatStoreID || !currentChat) {
      logger.error('User, store ID, or chat context not available.');
      return;
    }

    const token = authService.getToken();
    if (!token) {
      logger.error('Authentication token not found.');
      return;
    }

    const newMessage: Message = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };

    // Update state immediately with user message
    const updatedMessages = [...currentChat.messages, newMessage];
    setCurrentChat({
      ...currentChat,
      messages: updatedMessages
    });

    // Setup loading state *before* the async call
    setIsLoading(true);
    setLoadingMode(mode); // Store the mode being used
    setElapsedTime(0); // Reset timer
    setCurrentStepIndex(0); // Reset step index
    // Clear any lingering intervals from previous runs (safety measure)
    if (timerIntervalId) clearInterval(timerIntervalId);
    if (stepIntervalId) clearInterval(stepIntervalId);
    
    // Timer and step intervals will be started by useEffect hooks reacting to isLoading=true

    try {
      // Use FormData to send the request, handling image uploads
      const formData = new FormData();
      formData.append('message', message);

      // --- DIAGNOSTIC LOG --- 
      logger.debug(`[DEBUG] handleSendMessage: Checking currentChat.conversation_id = "${currentChat?.conversation_id}"`);
      // --- END DIAGNOSTIC LOG --- 

      // Only append conversation_id if it's not a temporary ID
      if (currentChat.conversation_id && !currentChat.conversation_id.startsWith('temp-')) {
        formData.append('conversation_id', currentChat.conversation_id);
      }
      if (mode) {
        formData.append('mode', mode);
      }
      if (image) {
        formData.append('image', image);
      }

      // Use production-ready HTTP client with automatic CSRF token handling
      const { default: httpClient } = await import('./services/httpClient');
      
      const fetchResponse = await httpClient.uploadFormData<ExtendedChatResponse>(
        `/api/chat/${chatStoreID}`,
        formData
      );

      // The response is already parsed by httpClient
      const responseData: ExtendedChatResponse = fetchResponse;
              logger.debug("[DEBUG] Chat response received:", responseData);

      const assistantResponse = responseData.message || responseData.response || 'Sorry, I encountered an error.';
      const newConversationId = responseData.conversation_id;

      // Handle context documents if available
      const contextDocuments = responseData.context_documents || [];

      // Prepare the assistant's message
      const assistantMessage: Message = {
        role: 'assistant',
        content: assistantResponse,
        timestamp: new Date(),
        contextDocuments: contextDocuments
      };

      // Update the state with the new assistant message and potentially new conversation ID
      setCurrentChat(prevChat => {
        if (!prevChat) return null; // Should not happen if we sent a message

        // Store the ID used for sending the message (might be temporary)
        const idBeforeSending = prevChat.conversation_id;

        // Create the fully updated chat object based on the response
        const updatedChat: Chat = {
          ...prevChat, // Keep previous messages initially added
          messages: [...prevChat.messages, assistantMessage], // Add the assistant's response
          conversation_id: newConversationId, // **Crucial: Update with ID from backend**
          // Update title/prompt only if backend provided new ones, otherwise keep existing
          title: responseData.title || prevChat.title,
          full_user_prompt: responseData.full_user_prompt || prevChat.full_user_prompt,
          // Keep full_title consistent with title for now, unless backend provides it
          full_title: responseData.full_title || responseData.title || prevChat.full_title || prevChat.title,
          updated_at: new Date() // Mark as updated now
        };

        // Immediately update the main 'chats' list state
        setChats(prevChatsList => {
          // Find the index of the chat using the ID it had *before* sending
          const chatIndex = prevChatsList.findIndex(c => c.conversation_id === idBeforeSending);

          if (chatIndex !== -1) {
            // If found (existing chat or temporary chat), replace it with the updated version
            const newChatsList = [...prevChatsList];
            newChatsList[chatIndex] = updatedChat;
            // Sort by updated_at to bring the most recently updated chat to the top
            return newChatsList.sort((a, b) => b.updated_at.getTime() - a.updated_at.getTime());
          } else if (idBeforeSending.startsWith('temp-')) {
             // If it was a temporary chat ID but wasn't found in the list (edge case, maybe deleted?)
             // Add the new chat to the list and sort.
             logger.warn(`Temporary chat with ID ${idBeforeSending} not found in chats list. Adding new chat.`);
             return [updatedChat, ...prevChatsList].sort((a, b) => b.updated_at.getTime() - a.updated_at.getTime());
          } else {
            // Chat not found and wasn't temporary - this indicates a potential state inconsistency.
            logger.error(`Chat with ID ${idBeforeSending} not found in chats list during update.`);
            // Avoid modifying state if the source chat isn't found
            return prevChatsList;
          }
        });

        // Return the updated chat object to set as the currentChat
        return updatedChat;
      });

      // Store last response details for potential permission alerts
      const validatedPermissions = (responseData.missing_permissions || []).filter(isValidMetaPermissionKey);
      setLastResponse({
        meta_permission_error: responseData.meta_permission_error,
        missing_permissions: validatedPermissions,
        response: assistantResponse,
        full_user_prompt: responseData.full_user_prompt || undefined
      });

      // Increment interaction count on successful send
      setInteractionCount(prev => prev + 1);

      // Invalidate cache for chat history after successful send
      invalidateCache(`/api/chat/history/${chatStoreID}`);
      
      // This still serves as a good sync mechanism, but the immediate update above should fix the visual bug.
      if (!currentChat.conversation_id || currentChat.conversation_id.startsWith('temp-')) {
        logger.debug("[DEBUG] New chat created, skipping immediate history fetch to prevent overwrite.");
      }

      // After successful chat send, refresh credits
      setCredits(await storeService.getStoreCredits(chatStoreID));

    } catch (error) {
      logger.error('Error sending message:', error);
      // Display error to the user with translation support
      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        // Check if the error message is a translation key
        if (error.message.startsWith('errors.')) {
          errorMessage = t(error.message);
        } else {
          errorMessage = error.message;
        }
      }
      const errorResponseMessage: Message = {
        role: 'assistant',
        content: `Error: ${errorMessage}`,
        timestamp: new Date()
      };
      // Only update if currentChat is not null
      if (currentChat) {
           setCurrentChat(prevChat => {
                if (!prevChat) return null;
                return {
                    ...prevChat,
                    messages: [...prevChat.messages, errorResponseMessage]
                };
            });
      }
    } finally {
      setIsLoading(false);
      // Reset loading state in finally block
      setLoadingMode(null);
      setCurrentStepIndex(0); 
      // Intervals will be cleared by useEffect hooks reacting to isLoading=false
    }
  }, [user, chatStoreID, currentChat, stepIntervalId, timerIntervalId, t]); // Added missing dependencies

  // Add useEffect for triggering feedback modal
  useEffect(() => {
    // Only trigger if not already shown in this session and count is 5 or more
    if (interactionCount >= 5 && !feedbackModalShownThisSession) {
      logger.debug('Interaction count reached 5, triggering feedback modal.');
      openFeedbackModal();
      setFeedbackModalShownThisSession(true);
    }
  }, [interactionCount, feedbackModalShownThisSession, openFeedbackModal]);

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, chatId: string) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedChatId(chatId);
    logger.debug("Menu clicked for chat ID:", chatId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Add effect to close menu when drawer closes
  useEffect(() => {
    if (!drawerOpen) {
      handleMenuClose();
    }
  }, [drawerOpen]);

  const handleRenameClick = () => {
    const chat = chats.find(c => c.conversation_id === selectedChatId);
    if (chat) {
      setNewTitle(chat.title);
      setRenameDialogOpen(true);
    }
    handleMenuClose();
  };

  const handleRenameConfirm = async () => {
    try {
      const token = authService.getToken();
      
      // Use fetchWithDeduplication instead of direct axios call
      await fetchWithDeduplication(`${API_URL}/api/chat/${selectedChatId}/rename`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ title: newTitle })
      });

      // Update local state
      setChats(prev => prev.map(chat => 
        chat.conversation_id === selectedChatId 
          ? { ...chat, title: newTitle, full_title: newTitle }
          : chat
      ));

      if (currentChat?.conversation_id === selectedChatId) {
        setCurrentChat(prev => prev ? { ...prev, title: newTitle, full_title: newTitle } : null);
      }
      
      // Invalidate the chat history cache since we've renamed a chat
      invalidateCache(`/api/chat/history/${chatStoreID}`);
      
    } catch (error) {
      logger.error('Error renaming chat:', error);
    }
    setRenameDialogOpen(false);
  };

  const handleDeleteClick = () => {
    logger.debug("Delete clicked for chat ID:", selectedChatId);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!selectedChatId) {
        logger.error("No chat ID selected for deletion");
        return;
      }
      
              logger.debug("Confirming deletion of chat ID:", selectedChatId);
      
      // Check if this is a temporary ID (client-side only)
      const isTemporaryId = selectedChatId.startsWith('temp-');
      
      // For temporary IDs, just update the UI
      if (isTemporaryId) {
                  logger.debug("Removing temporary chat from UI only:", selectedChatId);
      } 
      // For MongoDB IDs, delete from server
      else {
        try {
          const token = authService.getToken();
          
          await fetchWithDeduplication(`${API_URL}/api/chat/${selectedChatId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          logger.info("Successfully deleted from server:", selectedChatId);
          
          // Invalidate the cache
          invalidateCache(`/api/chat/history/${chatStoreID}`);
        } catch (error) {
          logger.error("Error deleting chat from server:", error);
        }
      }

      // Always update local state 
      setChats(prev => prev.filter(chat => chat.conversation_id !== selectedChatId));
      if (currentChat?.conversation_id === selectedChatId) {
        setCurrentChat(null);
        createNewChat();
      }
      
    } catch (error) {
      logger.error('Error deleting chat:', error);
    }
    setDeleteDialogOpen(false);
  };

  // Initialize Meta SDK and validate stored token on app startup (once only)
  useEffect(() => {
    // Only initialize once
    if (sdkInitialized.current) {
      return;
    }
    
    // Initialize Meta SDK
    const initializeMeta = async () => {
      try {
        logger.debug('Initializing Meta SDK on app startup (once only)...');
        sdkInitialized.current = true;
        
        // Initialize the SDK
        await initMetaSDK();
        
        // Don't do token validation here - let the MetaPermissionsContext handle it
        // This prevents duplicate validation and race conditions
        logger.debug('Meta SDK initialization complete, further validation handled by contexts');
      } catch (error) {
                  logger.error('Error initializing Meta SDK:', error);
      }
    };
    
    // Call initialization
    initializeMeta();
  }, []); // Empty dependency array - run only once on startup

  // Initialize console filter for Recharts warnings
  useEffect(() => {
    // Activate console filter to suppress Recharts logUtils warnings
    consoleFilter.activate();
    
    // Additional protection: Override console methods directly as a fallback
    const originalWarn = console.warn;
    const originalError = console.error;
    
    const enhancedWarn = (...args: unknown[]) => {
      const message = args.join(' ').toLowerCase();
      // Check for multiple warning patterns with case-insensitive matching
      if (
        message.includes('the width(') && message.includes('height(') && message.includes('chart should be') ||
        message.includes('logutils.js') ||
        message.includes('logutils') ||
        message.includes('responsivecontainer') && message.includes('greater than 0') ||
        message.includes('minwidth') && message.includes('minheight') && message.includes('aspect') ||
        message.includes('please check the style of container') ||
        message.includes('add a minwidth') && message.includes('or use') ||
        message.includes('to control the height and width') ||
        (message.includes('width') && message.includes('height') && message.includes('chart') && message.includes('greater than 0'))
      ) {
        // Suppress these warnings silently
        return;
      }
      originalWarn.apply(console, args);
    };
    
    const enhancedError = (...args: unknown[]) => {
      const message = args.join(' ').toLowerCase();
      // Check for error patterns that might be related to Recharts
      if (
        message.includes('the width(') && message.includes('height(') && message.includes('chart should be') ||
        message.includes('logutils.js') ||
        message.includes('logutils') ||
        (message.includes('width') && message.includes('height') && message.includes('chart') && message.includes('greater'))
      ) {
        // Suppress these errors silently
        return;
      }
      originalError.apply(console, args);
    };
    
    // Apply enhanced console methods
    console.warn = enhancedWarn;
    console.error = enhancedError;
    
    // Cleanup function to deactivate filter when app unmounts
    return () => {
      consoleFilter.deactivate();
      // Restore original console methods
      console.warn = originalWarn;
      console.error = originalError;
    };
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const exportChatToPDF = useCallback(async () => {
    if (!currentChat || !currentChat.messages.length) {
      logger.warn('No chat available to export');
      return;
    }

    try {
      // Dynamic import to avoid bundling issues
      const { jsPDF } = await import('jspdf');
      const pdf = new jsPDF();

      // PDF settings
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const margin = 20;
      const maxWidth = pageWidth - (margin * 2);
      let yPosition = margin;
      const lineHeight = 7;
      const messageSpacing = 10;

      // Add header
      pdf.setFontSize(16);
      pdf.setFont('helvetica', 'bold');
      const chatTitle = currentChat.title || t('chat.untitledChat');
      pdf.text(chatTitle, margin, yPosition);
      yPosition += 15;

      // Add chat metadata
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      const exportDate = new Date().toLocaleString();
      pdf.text(`${t('chat.export.exportedOn')}: ${exportDate}`, margin, yPosition);
      yPosition += 10;

      if (currentChat.timestamp) {
        const chatDate = new Date(currentChat.timestamp).toLocaleString();
        pdf.text(`${t('chat.export.chatCreated')}: ${chatDate}`, margin, yPosition);
        yPosition += 15;
      }

      // Add messages
      currentChat.messages.forEach((message) => {
        // Check if we need a new page
        if (yPosition > pageHeight - 50) {
          pdf.addPage();
          yPosition = margin;
        }

        // Role header
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        const roleText = message.role === 'user' ? (user?.email || 'User') : 'D-Unit Agent';
        pdf.text(roleText, margin, yPosition);
        yPosition += lineHeight;

        // Timestamp
        if (message.timestamp) {
          pdf.setFontSize(8);
          pdf.setFont('helvetica', 'normal');
          const timeText = formatTime(new Date(message.timestamp));
          pdf.text(timeText, margin, yPosition);
          yPosition += lineHeight;
        }

        // Message content
        pdf.setFontSize(10);
        pdf.setFont('helvetica', 'normal');
        
        // Clean markdown and split text into lines
        const cleanContent = message.content
          .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markdown
          .replace(/\*(.*?)\*/g, '$1') // Remove italic markdown
          .replace(/`(.*?)`/g, '$1') // Remove code markdown
          .replace(/#{1,6}\s/g, '') // Remove headers
          .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Convert links to text
          .replace(/\n\s*\n/g, '\n') // Remove extra line breaks
          .trim();

        const lines = pdf.splitTextToSize(cleanContent, maxWidth);
        
        // Add each line
        lines.forEach((line: string) => {
          if (yPosition > pageHeight - 30) {
            pdf.addPage();
            yPosition = margin;
          }
          pdf.text(line, margin, yPosition);
          yPosition += lineHeight;
        });

        yPosition += messageSpacing;
      });

      // Generate filename
      const filename = `chat-${chatTitle.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`;

      // Save the PDF
      pdf.save(filename);

      logger.info('Chat exported to PDF successfully');
    } catch (error) {
      logger.error('Error exporting chat to PDF:', error);
      // You could add a toast notification here to inform the user of the error
    }
  }, [currentChat, t, user?.email]);

  if (!user?.id_store) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height="100vh" 
        // Replace hardcoded color with theme-aware value
        bgcolor="background.default">
        <Typography variant="body1" color="text.secondary">{t('chat.emptyStates.storeInfoNotAvailable')}</Typography>
      </Box>
    );
  }

  return (
    <ErrorBoundary>
      <CSRFErrorBoundary maxRetries={3}>
        <Box sx={{ 
          minHeight: '100vh', 
          bgcolor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#F5F5F5'
        }}>
        <NavigationWrapper />
        { (loading || !i18n.isInitialized) ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100vh',
              color: theme.palette.text.primary
            }}
          >
            <CircularProgress sx={{ color: '#00A3FF', mb: 2 }} />
            <Typography variant="h6">
              {t('chat.loadingConsole.notReady')}
            </Typography>
          </Box>
        ) : (
          <PageContainer sx={{}} noPadding>
            <Box sx={{ 
              display: 'flex', 
              height: 'calc(100vh - 64px)',
              position: 'relative',
              width: '100%',
              bgcolor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#F5F5F5',
              borderRight: 'none', // Remove any potential border
            }}>
              <Drawer
                variant="persistent"
                open={drawerOpen}
                sx={{
                  width: DRAWER_WIDTH,
                  flexShrink: 0,
                  position: 'fixed',
                  height: '100%',
                  zIndex: 1200,
                  pointerEvents: drawerOpen ? 'auto' : 'none',
                  '& .MuiDrawer-paper': {
                    width: DRAWER_WIDTH,
                    boxSizing: 'border-box',
                    bgcolor: theme.palette.mode === 'dark' ? '#2D2D2D' : '#F5F5F5', // Grey color
                    color: 'text.primary',
                    border: 'none',
                    boxShadow: theme.palette.mode === 'dark' ? 'none' : '2px 0px 8px rgba(0, 0, 0, 0.05)',
                    transition: 'transform 0.3s ease-in-out',
                    transform: drawerOpen ? 'translateX(0)' : 'translateX(-100%)',
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    zIndex: 1200,
                    borderRadius: '8px',
                    pointerEvents: drawerOpen ? 'auto' : 'none',
                  },
                }}
              >
                <Box sx={{ p: 2, display: 'flex', alignItems: 'center' }}>
                  <img src={logo} alt="LaNube" height="30" />
                </Box>
                <Button
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={createNewChat}
                  sx={{
                    mx: 2,
                    mb: 2,
                    bgcolor: '#00A3FF',
                    color: 'white',
                    '&:hover': { bgcolor: '#0082CC' },
                    textTransform: 'none',
                    borderRadius: '8px',
                  }}
                >
                  {t('chat.startNewChatButton')}
                </Button>
                <List sx={{ flexGrow: 1, overflow: 'auto', px: 2 }}>
                  {chats.length === 0 ? (
                    <Box sx={{ textAlign: 'center', color: '#666', mt: 4 }}>
                      <Typography variant="body2" key="no-chat-1">{t('chat.emptyStates.noChatHistory')}</Typography>
                      <Typography variant="body2" key="no-chat-2">{t('chat.emptyStates.startNewChat')}</Typography>
                    </Box>
                  ) : (
                    (() => {
                      let currentGroup: string | null = null;
                      const listItems: React.ReactNode[] = [];

                      chats.forEach((chat) => {
                        // Ensure chat.updated_at is a valid Date object before processing
                        if (!chat.updated_at || isNaN(chat.updated_at.getTime())) {
                          logger.warn(`Skipping chat ${chat.conversation_id} due to invalid updated_at date.`);
                          return; // Skip this chat if the date is invalid
                        }
                        
                        const chatGroup = getChatGroup(chat.updated_at, t);

                        // Check if the group has changed
                        if (chatGroup !== currentGroup) {
                          // Add header for the new group
                          listItems.push(
                            <Typography
                              key={`header-${chatGroup}`}
                              variant="overline" // Use overline for a smaller header
                              sx={{
                                display: 'block', 
                                px: 1, 
                                py: 0.5,
                                mt: listItems.length > 0 ? 1.5 : 0, // Add margin top except for the first header
                                color: 'text.secondary',
                                fontWeight: 600,
                                fontSize: '0.7rem', // Slightly smaller font
                                textTransform: 'uppercase' // Make it uppercase
                              }}
                            >
                              {chatGroup}
                            </Typography>
                          );
                          currentGroup = chatGroup; // Update the current group
                        }

                        // Add the chat list item (existing logic)
                        listItems.push(
                          <Tooltip
                            title={chat.full_user_prompt || chat.title}
                            key={`tooltip-${chat.conversation_id}`} // Use chat.conversation_id for key
                            placement="right"
                            arrow
                            PopperProps={{
                              sx: {
                                marginLeft: '5px !important',
                                zIndex: 1350,
                                '& .MuiTooltip-tooltip': {
                                  bgcolor: '#333',
                                  color: 'white',
                                  fontSize: '0.8rem',
                                  maxWidth: '300px',
                                  padding: '8px 12px',
                                  borderRadius: '4px',
                                },
                                '& .MuiTooltip-arrow': {
                                  color: '#333',
                                },
                              }
                            }}
                          >
                            <ListItem
                              key={`item-${chat.conversation_id}`} // Use chat.conversation_id for key
                              onClick={() => setCurrentChat(chat)}
                              sx={{
                                cursor: 'pointer',
                                borderRadius: 2,
                                mb: 0.5, // Reduce margin bottom slightly
                                bgcolor: currentChat?.conversation_id === chat.conversation_id 
                                  ? theme.palette.mode === 'dark' 
                                    ? 'rgba(255, 255, 255, 0.05)' 
                                    : 'rgba(0, 163, 255, 0.1)'
                                  : 'transparent',
                                '&:hover': {
                                  bgcolor: theme.palette.mode === 'dark' 
                                    ? 'rgba(255, 255, 255, 0.05)' 
                                    : '#F0F0F0'
                                },
                                display: 'flex',
                                justifyContent: 'space-between',
                                pr: 1, // Keep right padding
                                pl: 1, // Add left padding to align text with header
                                position: 'relative',
                                width: '100%',
                              }}
                            >
                              <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                flexGrow: 1,
                                width: 'calc(100% - 40px)',
                                overflow: 'hidden' // Prevent text overflow issues
                              }}>
                                <ListItemIcon sx={{ minWidth: 35, mr: 0.5 }}> {/* Adjust icon margin */} 
                                  <ChatIcon sx={{ color: '#00A3FF', fontSize: '1.1rem' }} /> {/* Slightly smaller icon */} 
                                </ListItemIcon>
                                <ListItemText
                                  primary={chat.title}
                                  primaryTypographyProps={{ noWrap: true, variant: 'body2' }} // Use body2 for slightly smaller text
                                />
                              </Box>
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  const chatId = chat.conversation_id;
                                  if (!chatId) {
                                    logger.error("Chat has no valid ID:", chat);
                                    return;
                                  }
                                  handleMenuClick(e, chatId);
                                  e.stopPropagation();
                                }}
                                sx={{ 
                                  opacity: 0,
                                  '.MuiListItem-root:hover &': { opacity: 1 },
                                  position: 'absolute',
                                  right: 4, // Adjust position
                                  top: '50%', // Center vertically
                                  transform: 'translateY(-50%)',
                                  bgcolor: currentChat?.conversation_id === chat.conversation_id 
                                    ? '#ffffff' 
                                    : 'transparent',
                                  '&:hover': {
                                    bgcolor: theme.palette.mode === 'dark' 
                                      ? 'rgba(255, 255, 255, 0.1)' 
                                      : '#f5f5f5'
                                  },
                                  padding: '6px', // Adjust padding
                                  // Make it more visible on the current chat
                                  ...(currentChat?.conversation_id === chat.conversation_id && {
                                    opacity: 0.7,
                                    '&:hover': { 
                                      opacity: 1,
                                      bgcolor: '#f0f0f0'
                                    }
                                  })
                                }}
                              >
                                <MoreVertIcon sx={{ fontSize: '1rem' }} /> {/* Slightly smaller icon */} 
                              </IconButton>
                            </ListItem>
                          </Tooltip>
                        );
                      });

                      return listItems; // Render the generated list items and headers
                    })()
                  )}
                </List>
              </Drawer>
              <Box component="main" sx={{ 
                flexGrow: 1, 
                height: '100vh', 
                display: 'flex', 
                flexDirection: 'column',
                position: 'relative',
                zIndex: 'auto',
                bgcolor: theme.palette.mode === 'dark' ? '#1E1E1E' : '#FFFFFF',
                width: '100%',
                alignItems: 'center',
                overflow: 'hidden',
                borderRight: 'none',
                borderTopLeftRadius: '12px',    // Explicitly set top-left
                borderTopRightRadius: '12px',   // Explicitly set top-right
                borderBottomLeftRadius: '12px', // Ensure bottom-left is rounded (matches input area below)
                borderBottomRightRadius: '12px', // Ensure bottom-right is rounded
              }}>
                {/* Chat History Arrow Indicator */}
                <Box
                  sx={{
                    position: 'fixed',
                    left: 0,
                    top: '50%',
                    transform: 'translateY(-50%)',
                    width: '20px',
                    height: '80px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    bgcolor: 'rgba(0, 163, 255, 0.9)',
                    borderTopRightRadius: '4px',
                    borderBottomRightRadius: '4px',
                    cursor: 'pointer',
                    zIndex: 1300,
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      width: '25px',
                      bgcolor: '#00A3FF',
                    },
                    boxShadow: '2px 0 5px rgba(0,0,0,0.1)'
                  }}
                  onClick={() => setDrawerOpen(true)}
                >
                  <KeyboardArrowRightIcon sx={{ color: 'white', transform: 'rotate(180deg)' }} />
                </Box>
                
                <Box
                  id="message-list-container"
                  sx={{
                    flexGrow: 1,
                    overflow: 'auto',
                    p: { xs: 2, sm: 3, md: 4 },
                    pb: { xs: '280px', sm: '260px', md: 4 },
                    width: '100%',
                    position: 'relative',
                    zIndex: 1,
                    boxSizing: 'border-box',
                    maxWidth: { md: '800px' },
                    mx: { xs: 0, md: 'auto' },
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: { xs: 'center', md: 'stretch' },
                  }}>
                  {currentChat?.messages.map((message, index) => (
                    <Box
                      key={`message-${index}-${message.content.substring(0, 10).replace(/\s/g, '')}`}
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        mb: 3,
                        alignItems: message.role === 'user' ? 'flex-end' : 'flex-start',
                        width: '100%',
                        maxWidth: { xs: '100%', sm: '100%', md: '800px' },
                      }}
                    >
                      {message.role === 'assistant' && (
                        <Typography 
                          key={`assistant-label-${index}`}
                          variant="caption" 
                          sx={{ mb: 0.5, color: '#666' }}
                        >
                          {t('chat.roleAgent')}
                        </Typography>
                      )}
                      <Box
                        className={message.role === 'assistant' ? 'markdown-content' : ''}
                        sx={{
                          maxWidth: { xs: '85%', sm: '80%', md: '90%' },
                          width: 'fit-content',
                          bgcolor: message.role === 'user' ? '#00A3FF' : '#FAFAFB',
                          color: message.role === 'user' ? 'white' : '#000000',
                          p: { xs: 1.5, sm: 2 },
                          borderRadius: '16px',
                          boxShadow: message.role === 'user'
                            ? '0px 2px 4px rgba(0, 0, 0, 0.1)'
                            : '0px 2px 4px rgba(0, 0, 0, 0.08)',
                          wordBreak: 'break-word',
                          overflowWrap: 'break-word',
                          '& .markdown-content p': { margin: 0 },
                          '& a': { 
                            color: message.role === 'user' ? 'white' : '#00A3FF',
                            textDecoration: 'underline' 
                          },
                          '& table': {
                            borderCollapse: 'collapse',
                            width: '100%',
                            my: 1,
                            border: '1px solid #ddd',
                          },
                          '& th, & td': {
                            border: '1px solid #ddd',
                            padding: '8px',
                            textAlign: 'left'
                          },
                          '& th': {
                            backgroundColor: message.role === 'user' ? '#0082CC' : '#e0e0e0',
                            fontWeight: 'bold'
                          }
                        }}
                      >
                        {message.role === 'user' ? (
                          <Typography 
                            key={`message-content-${index}`}
                            variant="body1" 
                            sx={{ whiteSpace: 'pre-wrap', color: 'white' }}
                          >
                            {message.content}
                          </Typography>
                        ) : (
                          <ReactMarkdown
                            key={`message-content-${index}`}
                            remarkPlugins={[remarkGfm]}
                            components={{
                              a: (props) => <a {...props} target="_blank" rel="noopener noreferrer" />,
                              p: ({ ref: _ref, ...rest }) => <Typography variant="body1" component="p" sx={{mb: 1}} {...rest} />,
                              h1: ({ ref: _ref, ...rest }) => <Typography variant="h4" component="h4" {...rest} />,
                              h2: ({ ref: _ref, ...rest }) => <Typography variant="h5" component="h5" {...rest} />,
                              h3: ({ ref: _ref, ...rest }) => <Typography variant="h6" component="h6" {...rest} />,
                              h4: ({ ref: _ref, ...rest }) => <Typography variant="subtitle1" component="h4" {...rest} />,
                              h5: ({ ref: _ref, ...rest }) => <Typography variant="body1" component="p" sx={{ fontWeight: 600 }} {...rest} />,
                              h6: ({ ref: _ref, ...rest }) => <Typography variant="body2" component="p" sx={{ fontWeight: 600 }} {...rest} />,
                            }}
                          >
                            {message.content}
                          </ReactMarkdown>
                        )}
                      </Box>
                      {/* STEP 2: Ensure timestamp is rendered and styled correctly */}
                      {message.timestamp && (
                        <Typography 
                          key={`timestamp-${index}`}
                          variant="caption" 
                          sx={{ 
                            mt: 0.5, // Keep margin-top for spacing
                            color: '#6c757d', // Use a standard secondary text color
                            alignSelf: message.role === 'user' ? 'flex-end' : 'flex-start' // Align with message bubble
                          }}
                        >
                          {formatTime(new Date(message.timestamp))} 
                        </Typography>
                      )}
                      {/* Add this section to render context documents */}
                      {message.contextDocuments && message.contextDocuments.length > 0 && (
                        <Box 
                          sx={{ 
                            mt: 1, 
                            p: 1, 
                            borderRadius: 1, 
                            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
                            fontSize: '0.75rem'
                          }}
                        >
                          <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block', mb: 0.5 }}>
                            {t('chat.contextDocuments.relatedInformation')}
                          </Typography>
                          {message.contextDocuments.map((doc, docIndex) => (
                            <Box key={docIndex} sx={{ mb: 1, p: 0.5, borderLeft: '2px solid', borderColor: 'primary.main', pl: 1 }}>
                              <Typography variant="caption" sx={{ fontWeight: 'bold', display: 'block' }}>
                                {doc.type === 'product_details' ? t('chat.contextDocuments.productInfo') : 
                                 doc.type === 'store_customers' ? t('chat.contextDocuments.customerInfo') : 
                                 doc.type === 'active_stores' ? t('chat.contextDocuments.storeInfo') : doc.type} 
                                {' '} 
                                <span style={{ color: theme.palette.text.secondary }}>
                                  ({t('chat.similarity')}: {(doc.similarity * 100).toFixed(1)}%)
                                </span>
                              </Typography>
                              
                              {/* Content based on document type */}
                              {isProductDocument(doc) && (
                                <Box sx={{ ml: 1 }}>
                                  {doc.content.products && doc.content.products.length > 0 && (
                                    <>
                                      <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: theme.palette.text.secondary }}>
                                        {t('chat.contextDocuments.topProducts')} ({doc.content.product_count ?? doc.content.products.length} total):
                                      </Typography>
                                      {doc.content.products.slice(0, 3).map((product, i) => (
                                        <Typography key={i} variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                          • {product.name} - ${product.price} ({product.stock ? `${product.stock} ${t('chat.customerData.inStock')}` : t('chat.customerData.outOfStock')})
                                        </Typography>
                                      ))}
                                    </>
                                  )}
                                </Box>
                              )}
                              
                              {isCustomerDocument(doc) && (
                                <Box sx={{ ml: 1 }}>
                                  <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: theme.palette.text.secondary }}>
                                    {t('chat.contextDocuments.customerBase')} ({doc.content.total_customers || 0} total):
                                  </Typography>
                                  {doc.content.customers && doc.content.customers.length > 0 ? (
                                    <>
                                      {doc.content.customers.slice(0, 2).map((customer, i) => (
                                        <Typography key={i} variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                          • {customer.name} - {customer.order_count || 0} {t('chat.customerData.orders')}, ${customer.total_spent || 0} {t('chat.customerData.spent')}
                                        </Typography>
                                      ))}
                                      <Typography variant="caption" sx={{ display: 'block', mt: 0.5, fontSize: '0.7rem' }}>
                                        {t('chat.customerData.avgCustomerSpend')} ${doc.content.average_spend_per_customer || 0}
                                      </Typography>
                                    </>
                                  ) : (
                                    <Typography variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                      {t('chat.contextDocuments.noCustomerData')}
                                    </Typography>
                                  )}
                                </Box>
                              )}
                              
                              {isStoreDocument(doc) && (
                                <Box sx={{ ml: 1 }}>
                                  <Typography variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                    • {doc.content.name} ({doc.content.business_type || t('chat.contextDocuments.unknownType')})
                                  </Typography>
                                  {doc.content.country && (
                                    <Typography variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                      • {t('chat.contextDocuments.locatedIn')} {doc.content.country.name || t('chat.contextDocuments.unknownLocation')}
                                    </Typography>
                                  )}
                                  {doc.content.social_media && Object.keys(doc.content.social_media).length > 0 && (
                                    <Typography variant="caption" component="div" sx={{ fontSize: '0.7rem' }}>
                                      • {t('chat.contextDocuments.connectedSocial')} {Object.keys(doc.content.social_media).join(', ')}
                                    </Typography>
                                  )}
                                </Box>
                              )}
                            </Box>
                          ))}
                        </Box>
                      )}
                    </Box>
                  ))}
                  {isLoading && (
                    <LoadingIndicator
                      mode={loadingMode}
                      elapsedTime={elapsedTime}
                      stepText={
                        (STEPS[loadingMode as keyof typeof STEPS] || STEPS.Default)[currentStepIndex] || t('chat.processing')
                      }
                    />
                  )}
                  <div ref={messagesEndRef} />
                </Box>
                <Box
                  sx={{
                    position: { xs: 'fixed', md: 'relative' },
                    bottom: { xs: '56px', md: 'auto' },
                    left: { xs: 0, md: 'auto' },
                    right: { xs: 0, md: 'auto' },
                    width: '100%',
                    borderTop: 1,
                    borderColor: 'divider',
                    bgcolor: theme.palette.mode === 'dark' ? '#2D2D2D' : '#F5F5F5',
                    borderTopLeftRadius: { xs: 0, md: '12px' },
                    borderTopRightRadius: { xs: 0, md: '12px' },
                    borderBottomLeftRadius: '12px',
                    borderBottomRightRadius: '12px',
                    zIndex: { xs: 2100, md: 'auto' },
                    boxShadow: { xs: '0 -1px 3px rgba(0,0,0,0.1)', md: 'none' },
                  }}
                >
                  <Box
                    sx={{
                      p: { xs: 2, sm: 2.5, md: 3 },
                      maxWidth: '800px',
                      mx: 'auto',
                      width: '100%',
                      position: 'relative',
                      zIndex: 1,
                      boxSizing: 'border-box',
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}>
                      <Box sx={{ flexGrow: 1 }}>
                        <ChatInputWrapper 
                          onSendMessage={handleSendMessage}
                          onManagePermissions={handleNavigateToMetaPermissions}
                          loading={isLoading}
                          lastResponse={lastResponse}
                        />
                      </Box>
                      {/* Toolbar removed from this row; moved below */}
                    </Box>
                  </Box>
                  <Box sx={{
                    display: 'flex',
                    justifyContent: { xs: 'center', sm: 'flex-end' },
                    mt: 0.5,
                    px: { xs: 1, sm: 0 }
                  }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: { xs: 0.5, sm: 1 },
                      px: { xs: 1, sm: 1.5 },
                      py: 0.5,
                      borderRadius: '8px',
                      border: 1,
                      borderColor: 'divider',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0, 163, 255, 0.05)',
                      flexWrap: 'wrap',
                      justifyContent: 'center'
                    }}>
                      <Tooltip title={t('chat.export.exportToPDF')} placement="top">
                        <span>
                          <IconButton
                            onClick={exportChatToPDF}
                            disabled={!currentChat || currentChat.messages.length <= 1}
                            size="small"
                            sx={{
                              height: { xs: '24px', sm: '28px' },
                              width: { xs: '24px', sm: '28px' },
                              bgcolor: 'transparent',
                              border: `1px solid ${theme.palette.mode==='dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,163,255,0.4)'}`,
                              '&:hover': { bgcolor: theme.palette.mode==='dark' ? 'rgba(255,255,255,0.08)' : 'rgba(0,163,255,0.15)'}
                            }}
                          >
                            <SaveIcon sx={{ fontSize: { xs: '0.8rem', sm: '1rem' }, color: theme.palette.mode==='dark' ? '#fff' : '#00A3FF' }} />
                          </IconButton>
                        </span>
                      </Tooltip>
                      {credits !== null && (
                        <Tooltip
                          title={t('chat.creditsTooltip')}
                          placement="top"
                          arrow
                          componentsProps={{
                            tooltip: {
                              sx: {
                                whiteSpace: 'pre-line',
                                fontSize: '0.75rem',
                                maxWidth: '280px',
                                lineHeight: 1.4,
                                padding: '12px 16px',
                                backgroundColor: theme.palette.mode === 'dark' ? 'rgba(50, 50, 50, 0.95)' : 'rgba(0, 0, 0, 0.9)',
                                border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`,
                                borderRadius: '8px',
                                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                              }
                            }
                          }}
                        >
                          <Typography variant="caption" sx={{
                            whiteSpace: 'nowrap',
                            fontWeight: 500,
                            fontSize: { xs: '0.7rem', sm: '0.75rem' },
                            cursor: 'help',
                            textDecoration: 'underline',
                            textDecorationStyle: 'dotted',
                            textUnderlineOffset: '2px',
                            '&:hover': {
                              textDecoration: 'underline',
                              textDecorationStyle: 'solid'
                            }
                          }}>
                            {t('chat.creditsRemaining')}: {credits.toLocaleString()}
                          </Typography>
                        </Tooltip>
                      )}
                    </Box>
                  </Box>
                </Box>
              </Box>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
                MenuListProps={{
                  onMouseLeave: handleMenuClose,
                  dense: true,
                  sx: { 
                    py: 0.5,
                    minWidth: '150px'
                  }
                }}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'left',
                }}
                slotProps={{
                  paper: {
                    sx: {
                      elevation: 4,
                      mt: 0.5
                    }
                  }
                }}
              >
                <MenuItem onClick={handleRenameClick} sx={{ color: 'text.primary', gap: 1, py: 1.5 }}>
                  <EditIcon fontSize="small" />
                  {t('chat.menu.rename')}
                </MenuItem>
                <MenuItem onClick={handleDeleteClick} sx={{ color: '#d32f2f', gap: 1, py: 1.5 }}>
                  <DeleteOutlineIcon fontSize="small" />
                  {t('chat.menu.delete')}
                </MenuItem>
              </Menu>

              <Dialog open={renameDialogOpen} onClose={() => setRenameDialogOpen(false)}>
                <DialogTitle>{t('chat.rename.dialogTitle')}</DialogTitle>
                <DialogContent>
                  <TextField
                    autoFocus
                    margin="dense"
                    label={t('chat.rename.inputLabel')}
                    type="text"
                    fullWidth
                    value={newTitle}
                    onChange={(e) => setNewTitle(e.target.value)}
                  />
                </DialogContent>
                <DialogActions>
                  <Button onClick={() => setRenameDialogOpen(false)}>{t('chat.dialog.cancelButton')}</Button>
                  <Button onClick={handleRenameConfirm}>{t('chat.rename.confirmButton')}</Button>
                </DialogActions>
              </Dialog>

              <Dialog
                open={deleteDialogOpen}
                onClose={() => setDeleteDialogOpen(false)}
                PaperProps={{
                  sx: {
                    borderRadius: 2,
                    maxWidth: '400px',
                    width: '90%'
                  }
                }}
              >
                <DialogTitle>{t('chat.delete.dialogTitle')}</DialogTitle>
                <DialogContent>
                  <Typography>{t('chat.delete.confirmationText')}</Typography>
                </DialogContent>
                <DialogActions>
                  <Button
                    onClick={() => setDeleteDialogOpen(false)}
                    sx={{
                      bgcolor: '#00A3FF',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#0082CC',
                      },
                      textTransform: 'none',
                      px: 3,
                      borderRadius: '8px',
                    }}
                  >
                    {t('chat.dialog.cancelButton')}
                  </Button>
                  <Button
                    onClick={handleDeleteConfirm}
                    sx={{
                      bgcolor: '#d32f2f',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#b71c1c',
                      },
                      textTransform: 'none',
                      px: 3
                    }}
                  >
                    {t('chat.delete.confirmButton')}
                  </Button>
                </DialogActions>
              </Dialog>
            </Box>
          </PageContainer>
        )}
        <ScrollToTop />
        <CookieBanner />
        </Box>
      </CSRFErrorBoundary>
    </ErrorBoundary>
  );
};

export default App;