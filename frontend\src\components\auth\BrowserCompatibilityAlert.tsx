import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  IconButton,
  Typography,
  Box
} from '@mui/material';
import { Close, Warning, Info } from '@mui/icons-material';
import { detectBrowserCompatibility, BrowserCompatibility } from '../../utils/browserCompatibility';
import logger from '../../utils/logger';



interface BrowserCompatibilityAlertProps {
  onDismiss?: () => void;
  showOnlyForProblematicBrowsers?: boolean;
}

const BrowserCompatibilityAlert: React.FC<BrowserCompatibilityAlertProps> = ({
  onDismiss,
  showOnlyForProblematicBrowsers = true
}) => {
  const [compatibility, setCompatibility] = useState<BrowserCompatibility | null>(null);
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    const compat = detectBrowserCompatibility();
    setCompatibility(compat);
    
    // Check if user has previously dismissed this alert
    const dismissedKey = `browser-compat-dismissed-${compat.browserName}`;
    const wasDismissed = localStorage.getItem(dismissedKey) === 'true';
    setDismissed(wasDismissed);
    
    logger.debug('Browser compatibility detected:', compat);
  }, []);

  const handleDismiss = () => {
    if (compatibility) {
      const dismissedKey = `browser-compat-dismissed-${compatibility.browserName}`;
      localStorage.setItem(dismissedKey, 'true');
    }
    setDismissed(true);
    onDismiss?.();
  };

  // Don't show if dismissed or no compatibility issues detected
  if (dismissed || !compatibility) {
    return null;
  }

  // Only show for problematic browsers if the flag is set
  if (showOnlyForProblematicBrowsers && !compatibility.requiresSpecialHandling) {
    return null;
  }

  const getAlertSeverity = (): 'warning' | 'info' => {
    if (compatibility.hasStrictPrivacySettings) {
      return 'warning';
    }
    return 'info';
  };

  const getIcon = () => {
    if (compatibility.hasStrictPrivacySettings) {
      return <Warning sx={{ fontSize: 20 }} />;
    }
    return <Info sx={{ fontSize: 20 }} />;
  };

  const getTitle = () => {
    if (compatibility.hasStrictPrivacySettings) {
      return `${compatibility.browserName} Privacy Settings Detected`;
    }
    return 'Browser Compatibility Notice';
  };

  const getDescription = () => {
    if (compatibility.browserName === 'Safari') {
      return (
        <div className="space-y-2">
          <p>Safari's privacy settings may interfere with Meta/Facebook login.</p>
          <p><strong>If login fails, try:</strong></p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Using Private Browsing mode</li>
            <li>Disabling "Prevent Cross-Site Tracking" in Safari Settings → Privacy</li>
            <li>Allowing cookies from "facebook.com" and "meta.com"</li>
          </ul>
        </div>
      );
    }
    
    if (compatibility.browserName === 'Firefox') {
      return (
        <div className="space-y-2">
          <p>Firefox's Enhanced Tracking Protection may block Meta login.</p>
          <p><strong>If login fails, try:</strong></p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Using Private Browsing mode</li>
            <li>Clicking the shield icon and disabling protection for this site</li>
            <li>Setting Enhanced Tracking Protection to "Standard"</li>
          </ul>
        </div>
      );
    }
    
    if (compatibility.browserName === 'Privacy Browser') {
      return (
        <div className="space-y-2">
          <p>Privacy-focused browsers may block third-party authentication.</p>
          <p><strong>If login fails, try:</strong></p>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Using incognito/private mode</li>
            <li>Temporarily disabling privacy protections</li>
            <li>Using a different browser for authentication</li>
          </ul>
        </div>
      );
    }
    
    return (
      <div className="space-y-2">
        <p>Your browser may have settings that interfere with Meta login.</p>
        <p><strong>If you experience login issues, try using incognito/private mode.</strong></p>
      </div>
    );
  };

  return (
    <Alert
      severity={getAlertSeverity()}
      sx={{ mb: 2 }}
      action={
        <IconButton
          aria-label="close"
          color="inherit"
          size="small"
          onClick={handleDismiss}
        >
          <Close fontSize="inherit" />
        </IconButton>
      }
    >
      <Box display="flex" alignItems="flex-start" gap={1}>
        {getIcon()}
        <Box>
          <AlertTitle sx={{ fontWeight: 'bold', mb: 1 }}>
            {getTitle()}
          </AlertTitle>
          <Typography variant="body2">
            {getDescription()}
          </Typography>
        </Box>
      </Box>
    </Alert>
  );
};

export default BrowserCompatibilityAlert;
