<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Enhanced Storage Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Enhanced Storage & Login Compatibility Test</h1>
        <p>This page tests the browser compatibility fixes implemented for D-Unit normal login.</p>
        
        <div class="test-section">
            <h2>🔧 Test Controls</h2>
            <button onclick="runStorageTest()">Test Enhanced Storage</button>
            <button onclick="runCompatibilityTest()">Test Login Compatibility</button>
            <button onclick="runFullTest()">Run All Tests</button>
            <button onclick="clearResults()">Clear Results</button>
        </div>

        <div id="results" class="test-section" style="display: none;">
            <h2>📊 Test Results</h2>
            <div id="results-content"></div>
        </div>

        <div class="test-section">
            <h2>📋 What These Tests Verify</h2>
            <ul>
                <li><strong>Storage Fallback Chain:</strong> localStorage → sessionStorage → cookies</li>
                <li><strong>Storage Verification:</strong> Ensures data is actually stored and retrievable</li>
                <li><strong>Browser Detection:</strong> Identifies browser type and capabilities</li>
                <li><strong>Compatibility Issues:</strong> Detects common problems that force incognito mode</li>
                <li><strong>Error Handling:</strong> Graceful degradation when storage methods fail</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎯 Expected Improvements</h2>
            <p>These fixes should eliminate the need for incognito mode in normal login by:</p>
            <ul>
                <li>Providing multiple storage fallback options</li>
                <li>Verifying storage actually works before relying on it</li>
                <li>Giving clear diagnostics about browser compatibility issues</li>
                <li>Handling storage quota and privacy setting problems gracefully</li>
            </ul>
        </div>
    </div>

    <script>
        // Enhanced Storage Service Test Implementation
        class EnhancedStorageService {
            static testStorageMethod(storage, key, value) {
                try {
                    storage.setItem(key, value);
                    const retrieved = storage.getItem(key);
                    storage.removeItem(key);
                    return retrieved === value;
                } catch {
                    return false;
                }
            }

            static setUserDataWithVerification(userData) {
                const userDataString = JSON.stringify(userData);
                const testKey = 'user';

                // Strategy 1: Try localStorage
                if (this.testStorageMethod(localStorage, testKey + '_test', 'test')) {
                    try {
                        localStorage.setItem(testKey, userDataString);
                        const retrieved = localStorage.getItem(testKey);
                        if (retrieved === userDataString) {
                            console.log('User data stored and verified in localStorage');
                            return { success: true, method: 'localStorage' };
                        }
                    } catch (error) {
                        console.warn('localStorage storage failed:', error);
                    }
                }

                // Strategy 2: Try sessionStorage
                if (this.testStorageMethod(sessionStorage, testKey + '_test', 'test')) {
                    try {
                        sessionStorage.setItem(testKey, userDataString);
                        const retrieved = sessionStorage.getItem(testKey);
                        if (retrieved === userDataString) {
                            console.log('User data stored and verified in sessionStorage');
                            return { success: true, method: 'sessionStorage' };
                        }
                    } catch (error) {
                        console.warn('sessionStorage storage failed:', error);
                    }
                }

                // Strategy 3: Try cookies
                try {
                    const encodedData = encodeURIComponent(userDataString);
                    const maxAge = 60 * 60 * 24 * 7; // 7 days
                    document.cookie = `${testKey}=${encodedData}; path=/; SameSite=Lax; Secure; max-age=${maxAge}`;
                    
                    const cookies = document.cookie.split(';');
                    const userCookie = cookies.find(cookie => cookie.trim().startsWith(`${testKey}=`));
                    if (userCookie) {
                        console.log('User data stored and verified in cookies');
                        return { success: true, method: 'cookies' };
                    }
                } catch (error) {
                    console.error('Cookie storage failed:', error);
                    return { success: false, method: 'none', error: error.message };
                }

                return { success: false, method: 'none', error: 'All storage methods failed' };
            }

            static getUserDataWithDiagnostics() {
                const availableMethods = [];
                
                if (this.testStorageMethod(localStorage, 'test', 'test')) {
                    availableMethods.push('localStorage');
                }
                if (this.testStorageMethod(sessionStorage, 'test', 'test')) {
                    availableMethods.push('sessionStorage');
                }
                if (navigator.cookieEnabled) {
                    availableMethods.push('cookies');
                }

                // Try to retrieve data (simplified for test)
                try {
                    const data = localStorage.getItem('user');
                    if (data) {
                        return { data: JSON.parse(data), source: 'localStorage', available: availableMethods };
                    }
                } catch {}

                try {
                    const data = sessionStorage.getItem('user');
                    if (data) {
                        return { data: JSON.parse(data), source: 'sessionStorage', available: availableMethods };
                    }
                } catch {}

                return { data: null, source: 'none', available: availableMethods };
            }
        }

        // Login Compatibility Service Test Implementation
        class LoginCompatibilityService {
            static getBrowserInfo() {
                const userAgent = navigator.userAgent.toLowerCase();
                const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
                const isFirefox = userAgent.includes('firefox');
                const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
                const isEdge = userAgent.includes('edge');
                const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

                return {
                    browser: isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : isEdge ? 'Edge' : 'Unknown',
                    isMobile,
                    userAgent: navigator.userAgent,
                    cookiesEnabled: navigator.cookieEnabled,
                    isSecureContext: window.isSecureContext,
                    protocol: window.location.protocol
                };
            }

            static async runCompatibilityCheck() {
                const issues = [];
                const recommendations = [];
                const browserInfo = this.getBrowserInfo();

                const storageSupport = {
                    localStorage: this.testLocalStorage(),
                    sessionStorage: this.testSessionStorage(),
                    cookies: this.testCookies()
                };

                if (!storageSupport.localStorage && !storageSupport.sessionStorage && !storageSupport.cookies) {
                    issues.push('No storage methods available - login will not persist');
                    recommendations.push('Enable cookies in your browser settings');
                }

                if (!browserInfo.cookiesEnabled) {
                    issues.push('Cookies are disabled');
                    recommendations.push('Enable cookies for this site');
                }

                if (browserInfo.protocol !== 'https:' && !window.location.hostname.includes('localhost')) {
                    issues.push('Not using HTTPS - some features may not work');
                    recommendations.push('Access the site using HTTPS');
                }

                if (browserInfo.browser === 'Safari' && browserInfo.isMobile) {
                    if (!storageSupport.localStorage) {
                        issues.push('Safari private browsing detected - storage limited');
                        recommendations.push('Exit private browsing mode for full functionality');
                    }
                }

                if (browserInfo.browser === 'Firefox') {
                    if (!storageSupport.cookies) {
                        issues.push('Firefox Enhanced Tracking Protection may be blocking storage');
                        recommendations.push('Add this site to Firefox exceptions or use Standard protection');
                    }
                }

                const overall = issues.length === 0 ? 'good' : 
                               issues.some(issue => issue.includes('No storage methods')) ? 'error' : 'warning';

                return {
                    overall,
                    issues,
                    recommendations,
                    storageSupport,
                    browserInfo
                };
            }

            static testLocalStorage() {
                try {
                    localStorage.setItem('test', 'test');
                    localStorage.removeItem('test');
                    return true;
                } catch {
                    return false;
                }
            }

            static testSessionStorage() {
                try {
                    sessionStorage.setItem('test', 'test');
                    sessionStorage.removeItem('test');
                    return true;
                } catch {
                    return false;
                }
            }

            static testCookies() {
                try {
                    document.cookie = 'test=1; SameSite=Lax';
                    const hasCookie = document.cookie.includes('test=1');
                    document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
                    return hasCookie;
                } catch {
                    return false;
                }
            }
        }

        // Test Functions
        function runStorageTest() {
            console.log('🧪 Running Enhanced Storage Test...');
            
            const testData = {
                email: '<EMAIL>',
                id_store: '123',
                name: 'Test User',
                active: 1,
                created_at: new Date().toISOString(),
                role: 'user'
            };

            const result = EnhancedStorageService.setUserDataWithVerification(testData);
            const diagnostics = EnhancedStorageService.getUserDataWithDiagnostics();

            displayResults('Storage Test Results', {
                storageResult: result,
                diagnostics: diagnostics,
                testData: testData
            }, result.success ? 'success' : 'error');
        }

        async function runCompatibilityTest() {
            console.log('🧪 Running Login Compatibility Test...');
            
            const result = await LoginCompatibilityService.runCompatibilityCheck();

            displayResults('Compatibility Test Results', result, result.overall);
        }

        async function runFullTest() {
            console.log('🧪 Running Full Test Suite...');
            
            runStorageTest();
            setTimeout(async () => {
                await runCompatibilityTest();
            }, 1000);
        }

        function displayResults(title, data, status) {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('results-content');
            
            resultsDiv.style.display = 'block';
            resultsDiv.className = `test-section ${status === 'good' || status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'error'}`;
            
            const statusText = status === 'good' || status === 'success' ? '✅ PASSED' : 
                              status === 'warning' ? '⚠️ WARNING' : '❌ FAILED';
            
            contentDiv.innerHTML += `
                <div class="status">${title}: ${statusText}</div>
                <pre>${JSON.stringify(data, null, 2)}</pre>
                <hr>
            `;
        }

        function clearResults() {
            const resultsDiv = document.getElementById('results');
            const contentDiv = document.getElementById('results-content');
            
            resultsDiv.style.display = 'none';
            contentDiv.innerHTML = '';
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            console.log('🚀 Enhanced Storage & Login Compatibility Test Page Loaded');
            console.log('Click the test buttons to verify the fixes work correctly.');
        });
    </script>
</body>
</html>
