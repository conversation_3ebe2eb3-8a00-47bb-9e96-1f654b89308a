import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogTitle, 
  DialogContent, 
  <PERSON>alog<PERSON><PERSON>, 
  Button, 
  Typography, 
  Box, 
  Accordion, 
  AccordionSummary, 
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material';
import { ExpandMore, Help } from '@mui/icons-material';
import { detectBrowserCompatibility } from '../../utils/browserCompatibility';

interface MetaLoginTroubleshootingProps {
  open: boolean;
  onClose: () => void;
}

const MetaLoginTroubleshooting: React.FC<MetaLoginTroubleshootingProps> = ({
  open,
  onClose
}) => {
  const [expandedPanel, setExpandedPanel] = useState<string | false>(false);
  const compatibility = detectBrowserCompatibility();

  const handleAccordionChange = (panel: string) => (
    _event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedPanel(isExpanded ? panel : false);
  };

  const troubleshootingSteps = [
    {
      id: 'incognito',
      title: 'Try Incognito/Private Mode',
      description: 'This is the most effective solution for most browser issues',
      steps: [
        'Open a new incognito/private browsing window',
        'Navigate to the D-Unit login page',
        'Try logging in with Meta/Facebook',
        'If successful, the issue is related to browser cookies or extensions'
      ],
      priority: 'high'
    },
    {
      id: 'cookies',
      title: 'Enable Third-Party Cookies',
      description: 'Meta login requires third-party cookies to function properly',
      steps: compatibility.browserName === 'Chrome' ? [
        'Click the three dots menu → Settings',
        'Go to Privacy and security → Cookies and other site data',
        'Select "Allow all cookies" or "Block third-party cookies in Incognito"',
        'Refresh the page and try again'
      ] : compatibility.browserName === 'Firefox' ? [
        'Click the menu button → Settings',
        'Go to Privacy & Security',
        'Under Enhanced Tracking Protection, select "Standard"',
        'Or click "Custom" and uncheck "Cookies"',
        'Refresh the page and try again'
      ] : compatibility.browserName === 'Safari' ? [
        'Go to Safari → Preferences → Privacy',
        'Uncheck "Prevent cross-site tracking"',
        'Under "Cookies and website data", select "Always allow"',
        'Refresh the page and try again'
      ] : [
        'Check your browser\'s privacy settings',
        'Allow third-party cookies for facebook.com and meta.com',
        'Disable tracking protection temporarily',
        'Refresh the page and try again'
      ],
      priority: 'high'
    },
    {
      id: 'extensions',
      title: 'Disable Browser Extensions',
      description: 'Ad blockers and privacy extensions can interfere with Meta login',
      steps: [
        'Temporarily disable ad blockers (uBlock Origin, AdBlock Plus, etc.)',
        'Disable privacy extensions (Privacy Badger, Ghostery, etc.)',
        'Disable VPN extensions if active',
        'Refresh the page and try logging in',
        'Re-enable extensions one by one to identify the problematic one'
      ],
      priority: 'medium'
    },
    {
      id: 'cache',
      title: 'Clear Browser Data',
      description: 'Cached data might be causing conflicts',
      steps: [
        'Clear browser cache and cookies',
        'Clear localStorage and sessionStorage',
        'Close all browser tabs',
        'Restart your browser',
        'Try logging in again'
      ],
      priority: 'medium'
    },
    {
      id: 'facebook',
      title: 'Check Facebook Account',
      description: 'Ensure your Facebook account is properly configured',
      steps: [
        'Log into Facebook directly to ensure your account is active',
        'Check if you have any security restrictions on your account',
        'Verify your email address is confirmed in Facebook',
        'Make sure your account isn\'t temporarily restricted'
      ],
      priority: 'low'
    }
  ];

  const getPriorityColor = (priority: string): 'error' | 'warning' | 'info' | 'default' => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      scroll="paper"
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" gap={1}>
          <Help />
          Meta/Facebook Login Troubleshooting
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        <Box mb={2}>
          <Typography variant="body2" color="text.secondary" paragraph>
            If you're having trouble logging in with Meta/Facebook, try these solutions in order:
          </Typography>
          
          {compatibility.requiresSpecialHandling && (
            <Box mb={2} p={2} bgcolor="warning.light" borderRadius={1}>
              <Typography variant="body2" fontWeight="bold">
                Browser Detected: {compatibility.browserName}
              </Typography>
              <Typography variant="body2">
                This browser has strict privacy settings that may interfere with Meta login.
              </Typography>
            </Box>
          )}
        </Box>

        {troubleshootingSteps.map((step) => (
          <Accordion
            key={step.id}
            expanded={expandedPanel === step.id}
            onChange={handleAccordionChange(step.id)}
            sx={{ mb: 1 }}
          >
            <AccordionSummary expandIcon={<ExpandMore />}>
              <Box display="flex" alignItems="center" gap={1} width="100%">
                <Typography variant="h6" component="div" flexGrow={1}>
                  {step.title}
                </Typography>
                <Chip 
                  label={step.priority.toUpperCase()} 
                  size="small" 
                  color={getPriorityColor(step.priority)}
                />
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Typography variant="body2" color="text.secondary" paragraph>
                {step.description}
              </Typography>
              <List dense>
                {step.steps.map((stepText, index) => (
                  <ListItem key={index} sx={{ py: 0.5 }}>
                    <ListItemText 
                      primary={`${index + 1}. ${stepText}`}
                      primaryTypographyProps={{ variant: 'body2' }}
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ))}

        <Box mt={3} p={2} bgcolor="info.light" borderRadius={1}>
          <Typography variant="body2" fontWeight="bold" gutterBottom>
            Still having issues?
          </Typography>
          <Typography variant="body2">
            Contact support with details about your browser, operating system, and any error messages you see.
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MetaLoginTroubleshooting;
