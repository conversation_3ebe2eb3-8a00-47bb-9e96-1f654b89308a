/**
 * Meta API Proxy Service
 * 
 * This service provides a compatibility layer that redirects legacy Meta API calls
 * to the new Social Media API endpoints. This allows the existing Meta page components
 * to work with the improved backend API without requiring a complete rewrite.
 */

import { apiService } from './apiService';
import axios from '../config/axios';
import { logger } from '../utils/logger';

export class MetaApiProxy {
  
  /**
   * Get current store ID from localStorage
   */
  private static getCurrentStoreId(): string | null {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.id_store || null;
    } catch {
      return null;
    }
  }
  /**
   * Proxy for Meta insights API calls - redirects to appropriate API endpoints
   */
  static async getInsights(
    pageId: string,
    metricType: string,
    since: string,
    until: string,
    platform: 'facebook' | 'instagram' = 'facebook',
    _accessToken?: string
  ) {
    try {
      logger.debug(`MetaApiProxy: Fetching ${metricType} for ${platform}`, { pageId, since, until });
      
      // Map legacy metric types to API endpoints
      switch (metricType) {
        case 'page_impressions':
          if (platform === 'facebook') {
            return await this.getFacebookInsights(pageId, 'page_impressions', since, until);
          } else {
            const storeId = this.getCurrentStoreId();
            if (!storeId) {
              logger.warn('No store ID available for Instagram insights');
              return [];
            }
            const response = await apiService.getInstagramInsights('last_30_days', storeId);
            return this.transformInstagramInsights(response, 'page_impressions');
          }
          
        case 'page_engagement':
          if (platform === 'facebook') {
            return await this.getFacebookInsights(pageId, 'page_engagement', since, until);
          } else {
            const storeId = this.getCurrentStoreId();
            if (!storeId) {
              logger.warn('No store ID available for Instagram insights');
              return [];
            }
            const response = await apiService.getInstagramInsights('last_30_days', storeId);
            return this.transformInstagramInsights(response, 'page_engagement');
          }
          
        case 'page_fans':
          if (platform === 'facebook') {
            return await this.getFacebookInsights(pageId, 'page_fans', since, until);
          } else {
            const storeId = this.getCurrentStoreId();
            if (!storeId) {
              logger.warn('No store ID available for Instagram profile');
              return [];
            }
            const profileData = await apiService.getInstagramProfile(storeId);
            return this.transformInstagramFollowersData(profileData);
          }
          
        case 'page_views_total':
          if (platform === 'facebook') {
            return await this.getFacebookInsights(pageId, 'page_views_total', since, until);
          } else {
            const storeId = this.getCurrentStoreId();
            if (!storeId) {
              logger.warn('No store ID available for Instagram insights');
              return [];
            }
            const response = await apiService.getInstagramInsights('last_30_days', storeId);
            return this.transformInstagramInsights(response, 'page_views_total');
          }
          
        default:
          logger.warn(`Unknown metric type: ${metricType}`);
          return [];
      }
    } catch (error) {
      logger.error('Error in MetaApiProxy.getInsights:', error);
      // Return empty array with error to maintain compatibility
      return [];
    }
  }
  
  /**
   * Get Facebook insights using the Meta API
   */
  private static async getFacebookInsights(
    pageId: string,
    metricType: string,
    since: string,
    until: string
  ) {
    try {
      const storeId = this.getCurrentStoreId();
      if (!storeId) {
        logger.warn('No store ID available for Facebook insights');
        return [];
      }
      
      logger.debug(`Fetching Facebook ${metricType} insights`, { pageId, since, until });
      
      const response = await axios.get(`/api/meta/${storeId}/insights`, {
        params: {
          page_id: pageId,
          metric_type: metricType,
          since,
          until,
          platform: 'facebook'
        }
      });
      
      // Transform response to legacy format
      if (response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && typeof response.data === 'object') {
        // If single object, wrap in array
        return [response.data];
      }
      
      return [];
      
    } catch (error: unknown) {
      logger.error(`Error fetching Facebook ${metricType} insights:`, error);
      
      // If the API doesn't support this metric yet, return empty data silently
      if (typeof error === "object" && error !== null && "response" in error &&
          (error as { response?: { status?: number } }).response?.status &&
          ((error as { response?: { status?: number } }).response!.status === 404 ||
           (error as { response?: { status?: number } }).response!.status === 501)) {
        logger.debug(`Facebook ${metricType} not yet supported by API`);
        return [];
      }
      
      throw error;
    }
  }

  /**
   * Get fallback metrics when API calls fail
   */
  static async getFallbackMetrics(_pageId: string) {
    try {
      const storeId = this.getCurrentStoreId();
      
      const fallbackData = {
        impressions: { total: 0 },
        engagement: { total: 0 },
        followers: { total: 0 },
        views: { total: 0 },
        lowDataWarning: true,
        source: 'social_media_api'
      };

      if (!storeId) {
        logger.warn('No store ID available for fallback metrics');
        return fallbackData;
      }

      // Try to get cached data from the Social Media API
      const instagramProfileResult = await Promise.allSettled([
        apiService.getInstagramProfile(storeId)
      ]);
      
      // If we have Instagram data
      const instagramProfile = instagramProfileResult[0];
      if (instagramProfile.status === 'fulfilled' && instagramProfile.value?.data) {
        const profileData = instagramProfile.value.data;
        if (typeof profileData === 'object' && 'followers_count' in profileData) {
          fallbackData.followers.total = profileData.followers_count || 0;
        }
      }

      return fallbackData;
    } catch (error) {
      logger.error('Error in getFallbackMetrics:', error);
      return {
        impressions: { total: 0 },
        engagement: { total: 0 },
        followers: { total: 0 },
        views: { total: 0 },
        lowDataWarning: true,
        source: 'error'
      };
    }
  }

  /**
   * Transform Instagram insights to legacy format
   */
  private static transformInstagramInsights(
    response: { data?: Record<string, unknown> }, 
    metricType?: string
  ) {
    if (!response?.data || typeof response.data !== 'object') {
      return [];
    }

    const insights = response.data;
    const result: Array<{ name: string; value: number; end_time: string }> = [];
    const currentDate = new Date().toISOString();

    // If a specific metric type is requested, only return that metric
    if (metricType) {
      const metricMapping: Record<string, string> = {
        'page_impressions': 'impressions',
        'page_engagement': 'reach',
        'page_views_total': 'profile_views'
      };
      
      const instagramKey = metricMapping[metricType];
      if (instagramKey && insights[instagramKey] !== undefined && typeof insights[instagramKey] === 'number') {
        result.push({
          name: metricType,
          value: insights[instagramKey] as number || 0,
          end_time: currentDate
        });
      }
      
      return result;
    }

    // Return all available metrics if no specific type requested
    if (insights.impressions !== undefined && typeof insights.impressions === 'number') {
      result.push({
        name: 'page_impressions',
        value: insights.impressions || 0,
        end_time: currentDate
      });
    }

    if (insights.reach !== undefined && typeof insights.reach === 'number') {
      result.push({
        name: 'page_engagement',
        value: insights.reach || 0,
        end_time: currentDate
      });
    }

    if (insights.profile_views !== undefined && typeof insights.profile_views === 'number') {
      result.push({
        name: 'page_views_total',
        value: insights.profile_views || 0,
        end_time: currentDate
      });
    }

    return result;
  }

  /**
   * Transform Instagram followers data to legacy format
   */
  private static transformInstagramFollowersData(response: { data?: Record<string, unknown> }) {
    if (!response?.data || typeof response.data !== 'object') {
      return [];
    }

    const profileData = response.data;
    const currentDate = new Date().toISOString();

    return [{
      name: 'page_fans',
      value: typeof profileData.followers_count === 'number' ? profileData.followers_count : 0,
      end_time: currentDate
    }];
  }
}