import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import authService from '../services/authService';
import logger from '../utils/logger';
import cookieService from '../services/cookieService';

// Enhanced storage service with verification and diagnostics
class EnhancedStorageService {
  private static testStorageMethod(storage: Storage, key: string, value: string): boolean {
    try {
      storage.setItem(key, value);
      const retrieved = storage.getItem(key);
      storage.removeItem(key);
      return retrieved === value;
    } catch {
      return false;
    }
  }

  static setUserDataWithVerification(userData: User): { success: boolean; method: string; error?: string } {
    const userDataString = JSON.stringify(userData);
    const testKey = 'user';

    // Strategy 1: Try localStorage
    if (this.testStorageMethod(localStorage, testKey + '_test', 'test')) {
      try {
        localStorage.setItem(testKey, userDataString);
        const retrieved = localStorage.getItem(testKey);
        if (retrieved === userDataString) {
          logger.debug('User data stored and verified in localStorage');
          return { success: true, method: 'localStorage' };
        }
      } catch (error) {
        logger.warn('localStorage storage failed:', error);
      }
    }

    // Strategy 2: Try sessionStorage
    if (this.testStorageMethod(sessionStorage, testKey + '_test', 'test')) {
      try {
        sessionStorage.setItem(testKey, userDataString);
        const retrieved = sessionStorage.getItem(testKey);
        if (retrieved === userDataString) {
          logger.debug('User data stored and verified in sessionStorage');
          return { success: true, method: 'sessionStorage' };
        }
      } catch (error) {
        logger.warn('sessionStorage storage failed:', error);
      }
    }

    // Strategy 3: Try cookies
    try {
      const encodedData = encodeURIComponent(userDataString);
      const maxAge = 60 * 60 * 24 * 7; // 7 days
      document.cookie = `${testKey}=${encodedData}; path=/; SameSite=Lax; Secure; max-age=${maxAge}`;

      // Verify cookie was set
      const cookies = document.cookie.split(';');
      const userCookie = cookies.find(cookie => cookie.trim().startsWith(`${testKey}=`));
      if (userCookie) {
        logger.debug('User data stored and verified in cookies');
        return { success: true, method: 'cookies' };
      }
    } catch (error) {
      logger.error('Cookie storage failed:', error);
      return { success: false, method: 'none', error: error instanceof Error ? error.message : 'Unknown error' };
    }

    return { success: false, method: 'none', error: 'All storage methods failed' };
  }

  static getUserDataWithDiagnostics(): { data: User | null; source: string; available: string[] } {
    const availableMethods: string[] = [];

    // Test what's available
    if (this.testStorageMethod(localStorage, 'test', 'test')) {
      availableMethods.push('localStorage');
    }
    if (this.testStorageMethod(sessionStorage, 'test', 'test')) {
      availableMethods.push('sessionStorage');
    }
    if (navigator.cookieEnabled) {
      availableMethods.push('cookies');
    }

    // Try to retrieve data
    // Strategy 1: localStorage
    try {
      const data = localStorage.getItem('user');
      if (data) {
        return { data: JSON.parse(data), source: 'localStorage', available: availableMethods };
      }
    } catch (error) {
      logger.warn('localStorage retrieval failed:', error);
    }

    // Strategy 2: sessionStorage
    try {
      const data = sessionStorage.getItem('user');
      if (data) {
        return { data: JSON.parse(data), source: 'sessionStorage', available: availableMethods };
      }
    } catch (error) {
      logger.warn('sessionStorage retrieval failed:', error);
    }

    // Strategy 3: cookies
    try {
      const cookies = document.cookie.split(';');
      const userCookie = cookies.find(cookie => cookie.trim().startsWith('user='));
      if (userCookie) {
        const encodedData = userCookie.split('=')[1];
        const decodedData = decodeURIComponent(encodedData);
        return { data: JSON.parse(decodedData), source: 'cookies', available: availableMethods };
      }
    } catch (error) {
      logger.warn('Cookie retrieval failed:', error);
    }

    return { data: null, source: 'none', available: availableMethods };
  }

  static clearAllUserData(): void {
    const results: string[] = [];

    // Clear localStorage
    try {
      localStorage.removeItem('user');
      results.push('localStorage: cleared');
    } catch {
      results.push('localStorage: failed');
    }

    // Clear sessionStorage
    try {
      sessionStorage.removeItem('user');
      results.push('sessionStorage: cleared');
    } catch {
      results.push('sessionStorage: failed');
    }

    // Clear cookies
    try {
      document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax';
      document.cookie = 'user=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax; Secure';
      results.push('cookies: cleared');
    } catch {
      results.push('cookies: failed');
    }

    logger.debug('Storage cleanup results:', results);
  }
}

// Browser compatibility service for normal login diagnostics
class LoginCompatibilityService {
  static getBrowserInfo() {
    const userAgent = navigator.userAgent.toLowerCase();
    const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
    const isFirefox = userAgent.includes('firefox');
    const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
    const isEdge = userAgent.includes('edge');
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    return {
      browser: isChrome ? 'Chrome' : isFirefox ? 'Firefox' : isSafari ? 'Safari' : isEdge ? 'Edge' : 'Unknown',
      isMobile,
      userAgent: navigator.userAgent,
      cookiesEnabled: navigator.cookieEnabled,
      isSecureContext: window.isSecureContext,
      protocol: window.location.protocol
    };
  }

  static async runCompatibilityCheck(): Promise<{
    overall: 'good' | 'warning' | 'error';
    issues: string[];
    recommendations: string[];
    storageSupport: {
      localStorage: boolean;
      sessionStorage: boolean;
      cookies: boolean;
    };
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    const browserInfo = this.getBrowserInfo();

    // Test storage support
    const storageSupport = {
      localStorage: this.testLocalStorage(),
      sessionStorage: this.testSessionStorage(),
      cookies: this.testCookies()
    };

    // Check for common issues
    if (!storageSupport.localStorage && !storageSupport.sessionStorage && !storageSupport.cookies) {
      issues.push('No storage methods available - login will not persist');
      recommendations.push('Enable cookies in your browser settings');
    }

    if (!browserInfo.cookiesEnabled) {
      issues.push('Cookies are disabled');
      recommendations.push('Enable cookies for this site');
    }

    if (browserInfo.protocol !== 'https:' && !window.location.hostname.includes('localhost')) {
      issues.push('Not using HTTPS - some features may not work');
      recommendations.push('Access the site using HTTPS');
    }

    // Browser-specific checks
    if (browserInfo.browser === 'Safari' && browserInfo.isMobile) {
      if (!storageSupport.localStorage) {
        issues.push('Safari private browsing detected - storage limited');
        recommendations.push('Exit private browsing mode for full functionality');
      }
    }

    if (browserInfo.browser === 'Firefox') {
      // Check for strict privacy settings
      if (!storageSupport.cookies) {
        issues.push('Firefox Enhanced Tracking Protection may be blocking storage');
        recommendations.push('Add this site to Firefox exceptions or use Standard protection');
      }
    }

    const overall = issues.length === 0 ? 'good' :
                   issues.some(issue => issue.includes('No storage methods')) ? 'error' : 'warning';

    return {
      overall,
      issues,
      recommendations,
      storageSupport
    };
  }

  private static testLocalStorage(): boolean {
    try {
      localStorage.setItem('test', 'test');
      localStorage.removeItem('test');
      return true;
    } catch {
      return false;
    }
  }

  private static testSessionStorage(): boolean {
    try {
      sessionStorage.setItem('test', 'test');
      sessionStorage.removeItem('test');
      return true;
    } catch {
      return false;
    }
  }

  private static testCookies(): boolean {
    try {
      document.cookie = 'test=1; SameSite=Lax';
      const hasCookie = document.cookie.includes('test=1');
      document.cookie = 'test=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/';
      return hasCookie;
    } catch {
      return false;
    }
  }
}

export interface User {
  email: string;
  id_store: string;  // Changed from 'number | string' to 'string' to align with backend model
  name: string | null;
  role?: string;
  active?: number;
  created_at?: string;
  updated_at?: string;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  googleLogin: (accessToken: string) => Promise<void>;
  metaLogin: (accessToken: string, platform?: 'facebook' | 'instagram') => Promise<void>;
  logout: () => void;
  isLoading: boolean;
  setUser: (user: User | null) => void;
  setIsAuthenticated: (value: boolean) => void;
  performLogoutActions: () => void;
  viewingStoreID: string | null;
  setViewStoreID: (id: string | null) => void;
}

// Define error interface for API responses
interface APIError {
  response?: {
    data?: {
      detail?: string;
      requires_2fa?: boolean;
      message?: string;
    };
  };
  message?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggingOut, setIsLoggingOut] = useState(false); // Add state for logout progress
  const [viewingStoreID, setViewStoreID] = useState<string | null>(null);

  // Helper method to trigger first-time cookie notice - MOVED HERE TO FIX INITIALIZATION ORDER
  const triggerFirstTimeCookieNotice = useCallback(() => {
    try {
      if (cookieService.isFirstTimeLogin() && !cookieService.hasConsent()) {
        // Dispatch custom event to show first-time cookie notice
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('show-first-time-cookie-notice'));
        }, 1000); // Small delay to let the dashboard load
      }
    } catch (error) {
      logger.error('Error checking first-time cookie notice:', error);
      // Continue without showing the popup if there's an error
    }
  }, []);



// Detect and handle third-party cookie blocking 
const testThirdPartyCookies = async (): Promise<boolean> => { 
  try { 
    document.cookie = "test=1; SameSite=None; Secure"; 
    const hasCookie = document.cookie.includes("test=1"); 
    // Clean up 
    document.cookie = "test=; Max-Age=0; path=/;"; 
    return hasCookie; 
  } catch { 
    return false; 
  } 
}; 

useEffect(() => {
    const initializeAuth = async () => {
      setIsLoading(true);

      // Run compatibility check
      const compatCheck = await LoginCompatibilityService.runCompatibilityCheck();

      if (compatCheck.overall === 'error') {
        logger.error('Critical browser compatibility issues detected:', compatCheck.issues);
      } else if (compatCheck.overall === 'warning') {
        logger.warn('Browser compatibility warnings:', compatCheck.issues);
      }

      logger.info('Browser compatibility check:', compatCheck);

      // Test third-party cookies
      const canUseThirdPartyCookies = await testThirdPartyCookies();
      if (!canUseThirdPartyCookies) {
        logger.warn('Third-party cookies appear to be blocked. Using fallback storage methods.');
      }

      try {
        const token = authService.getToken();
        const userResult = EnhancedStorageService.getUserDataWithDiagnostics();

        logger.info(`User data source: ${userResult.source}, Available methods: ${userResult.available.join(', ')}`);

        if (token && userResult.data) {
          setUser({
            email: userResult.data.email,
            id_store: userResult.data.id_store,
            name: userResult.data.name || null,
            active: userResult.data.active,
            created_at: userResult.data.created_at,
            updated_at: userResult.data.updated_at,
            role: userResult.data.role
          });
          setIsAuthenticated(true);
        }
      } catch (error) {
        logger.error('Auth initialization error:', error);
        EnhancedStorageService.clearAllUserData();
        authService.clearTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Define the core logout actions in a separate function for aggressive clearing
  const performLogoutActions = useCallback(async (skipReload: boolean = false) => {
    logger.info('Performing aggressive session clear' + (skipReload ? ' without reload' : ''));

    try {
      // Clear CSRF tokens
      const { csrfService } = await import('../services/csrfService');
      try {
        csrfService.clearToken();
      } catch (error: unknown) {
        logger.warn('Failed to clear CSRF tokens:', error);
      }

      // Clear all tokens and user data
      authService.clearTokens();
      cookieService.clearAllCookies();
      EnhancedStorageService.clearAllUserData();
      localStorage.clear();
      sessionStorage.clear();
      document.cookie = 'test=; Max-Age=0; path=/;';

      // Reset state
      setUser(null);
      setIsAuthenticated(false);
      setViewStoreID(null);
    } catch (error) {
      logger.error('Error during session clear:', error);
    } finally {
      if (!skipReload) {
        window.location.href = '/login';
      }
    }
  }, []);

  const login = useCallback(async (email: string, password: string) => {
    try {
       await performLogoutActions(true);
      logger.info('Sending login request...');
      // Get the token using the auth service
      const tokenData = await authService.login(email, password);

      logger.info('Token response received');
      
      // Check if this is a 2FA response
      if (tokenData.requires_2fa) {
        logger.info('2FA required, throwing response for component to handle');
        throw { response: { data: { requires_2fa: true, message: tokenData.message } } };
      }
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error('No access token received');
      }

      // Clear any existing auth data only after successful token response
      authService.clearTokens();
      localStorage.removeItem('user');

      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }

      // Get user data
      try {
        const userData = await authService.getUserProfile();

        // Store user data with verification
        const storageResult = EnhancedStorageService.setUserDataWithVerification(userData);

        if (!storageResult.success) {
          logger.error('Failed to store user data:', storageResult.error);
          // Still continue with login but warn user
          logger.warn('Login successful but user data storage failed. You may need to log in again after closing the browser.');
        } else {
          logger.info(`User data stored successfully using ${storageResult.method}`);
        }

        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });

        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('Error fetching user data:', userError);
        throw new Error('Failed to fetch user data after login');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Login error:', apiError);
      logger.error('Login error details:', apiError.response?.data);
      
      // Don't clear tokens if this is a 2FA response
      if (!apiError.response?.data?.requires_2fa) {
        authService.clearTokens();
        EnhancedStorageService.clearAllUserData();
      }
      
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice, performLogoutActions]);

  const googleLogin = useCallback(async (accessToken: string) => {
    try {
      await performLogoutActions(true);
      logger.info('Processing Google access token...');
      
      // Exchange Google access token for our app token
      const tokenData = await authService.googleLogin(accessToken);
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error('No access token received from Google login');
      }
      
      // Clear any existing auth data
      authService.clearTokens();
      localStorage.removeItem('user');
      
      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }
      
      // Get user data
      try {
        const userData = await authService.getUserProfile();
        
        // Store user data with verification
        const storageResult = EnhancedStorageService.setUserDataWithVerification(userData);

        if (!storageResult.success) {
          logger.error('Failed to store user data:', storageResult.error);
          logger.warn('Login successful but user data storage failed. You may need to log in again after closing the browser.');
        } else {
          logger.info(`User data stored successfully using ${storageResult.method}`);
        }
        
        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });
        
        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('Error fetching user data after Google login:', userError);
        throw new Error('Failed to fetch user data after Google login');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Google login error:', apiError);
      logger.error('Google login error details:', apiError.response?.data);
      
      authService.clearTokens();
      localStorage.removeItem('user');
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice, performLogoutActions]);

  const metaLogin = useCallback(async (accessToken: string, platform: 'facebook' | 'instagram' = 'facebook') => {
    try {
      await performLogoutActions(true);
      logger.info(`AuthContext: Processing ${platform} access token...`);
      
      // Exchange Meta token for our app token
      const tokenData = await authService.metaLogin(accessToken, platform);
      
      const { access_token } = tokenData;
      if (!access_token) {
        throw new Error(`No access token received from ${platform} login`);
      }
      
      // Clear any existing auth data
      authService.clearTokens();
      localStorage.removeItem('user');
      
      // Set the token using authService for consistent cookie-based storage
      authService.setToken(access_token);
      if (tokenData.refresh_token) {
        authService.setRefreshToken(tokenData.refresh_token);
      }
      
      // Get user data
      try {
        const userData = await authService.getUserProfile();
        
        // Store user data with verification
        const storageResult = EnhancedStorageService.setUserDataWithVerification(userData);

        if (!storageResult.success) {
          logger.error('Failed to store user data:', storageResult.error);
          logger.warn('Login successful but user data storage failed. You may need to log in again after closing the browser.');
        } else {
          logger.info(`User data stored successfully using ${storageResult.method}`);
        }
        
        // Update state
        setUser({
          email: userData.email,
          id_store: userData.id_store,
          name: userData.name || null,
          active: userData.active,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          role: userData.role
        });
        
        setIsAuthenticated(true);
        
        // Initialize CSRF token after successful login (non-blocking with delay)
        setTimeout(() => {
          import('../services/csrfService').then(({ csrfService }) => {
            csrfService.initializeAfterLogin();
          }).catch(() => {
            // Ignore errors - CSRF initialization is optional
          });
        }, 100); // Small delay to ensure auth token is properly set
        
        // Trigger first-time cookie notice if applicable
        triggerFirstTimeCookieNotice();
      } catch (userError) {
        logger.error('AuthContext: Error fetching user data:', userError);
        throw new Error('Failed to fetch user data after Meta authentication');
      }
    } catch (error: unknown) {
      const apiError = error as APIError;
      logger.error('Meta login error:', apiError);
      logger.error('Meta login error details:', apiError.response?.data);
      
      authService.clearTokens();
      localStorage.removeItem('user');
      
      throw apiError;
    }
  }, [triggerFirstTimeCookieNotice, performLogoutActions]);



  // The main logout function, which prevents duplicates and calls the action.
  const logout = useCallback(() => {
    if (isLoggingOut) {
      logger.warn('[AuthContext] Logout already in progress, ignoring duplicate request.');
      return;
    }
    setIsLoggingOut(true);
    logger.info('[AuthContext] Logout requested. Performing aggressive logout...');
    performLogoutActions();
    // No code will execute after this point due to page navigation
  }, [isLoggingOut, performLogoutActions]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        login,
        googleLogin,
        metaLogin,
        logout,
        isLoading,
        setUser,
        setIsAuthenticated,
        performLogoutActions,
        viewingStoreID,
        setViewStoreID
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
