import "vitest";
import { render, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Dashboard from '../components/Dashboard';
import type { User } from '../contexts/AuthContext';

// Mock i18next translation hook to just return the key
vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key, i18n: { language: 'en' } }),
}));

// Mock AuthContext (used only if storeUser not provided)
vi.mock('../contexts/AuthContext', () => ({
  useAuth: () => ({ user: { id_store: '1', name: 'Test User' } }),
}));

// Mock services that perform network requests in Dashboard
vi.mock('../services/storeService', () => ({
  storeService: {
    fetchStoreAnalysis: vi.fn(() => Promise.resolve({})),
    fetchProductList: vi.fn(() => Promise.resolve({ products: [], store_aggregations: {} })),
    fetchSeoRecommendations: vi.fn(() => Promise.resolve([])),
    fetchFeedback: vi.fn(() => Promise.resolve([])),
  },
}));

const theme = createTheme();

describe('Dashboard', () => {
  it('renders dashboard title', async () => {
    render(
      <ThemeProvider theme={theme}>
        <MemoryRouter>
          <Dashboard storeUser={{ id_store: '1', name: 'Demo Store' } as User} />
        </MemoryRouter>
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('dashboard.title')).toBeInTheDocument();
    });
  });
});