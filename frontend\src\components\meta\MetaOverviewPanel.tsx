import * as React from 'react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  CircularProgress, 
  Grid, 
  Alert,
  Paper,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import { MetaPage } from '../../services/types';
import { isMetaAuthReady } from '../../services/authChecker';
import { MetaAuthRequired } from './MetaAuthRequired';
import { logger } from '../../utils/logger';
import { useTranslation } from 'react-i18next';
import { httpClient } from '../../services/httpClient';
import { useAuth } from '../../contexts/AuthContext';
import { MetaInsightChart } from './MetaInsightChart';
import { metaHistoricalService, HistoricalMetrics } from '../../services/metaHistoricalService';
import { getMetaTimeRangePresets } from '../../services/metaTimeRanges';

interface MetaOverviewPanelProps {
  page?: MetaPage;
}

type TimeRange = '7d' | '30d' | '90d' | 'lifetime';

interface TimeRangeConfig {
  value: TimeRange;
  label: string;
  description: string;
}

// Format number with thousands separators and abbreviate large numbers
const formatNumber = (value: number): string => {
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  } else if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
};


/**
 * Component for displaying key Meta metrics in a simple overview panel
 */
const MetaOverviewPanelComponent: React.FC<MetaOverviewPanelProps> = ({
  page
}: MetaOverviewPanelProps) => {
  const { t } = useTranslation();
  
  // Debug log to confirm component is loaded
  React.useEffect(() => {
    console.log('🔥 NEW MetaOverviewPanel loaded with page:', page?.platform, page?.id);
    console.log('🚀 This should show 6 Instagram metrics if platform is instagram');
  }, [page]);
  const { user } = useAuth();
  const [loading, setLoading] = useState<boolean>(true); // Start with loading true
  const [error, setError] = useState<string | null>(null);
  const [authError, setAuthError] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  // Removed lastFetchTime - no longer needed since we removed the time-based check
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>('30d');
  
  // Refs to prevent infinite loops
  const previousPageIdRef = useRef<string | undefined>();
  const previousTimeRangeRef = useRef<TimeRange>('30d');
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const hasInitialFetchedRef = useRef(false); // Track if we've done initial fetch

  // Time range configuration - dynamically generated based on platform
  const timeRangeOptions: TimeRangeConfig[] = React.useMemo(() => {
    const platform = page?.platform || 'facebook';
    const allowedPresets = getMetaTimeRangePresets(platform, 'standard');
    
    // Filter to only include presets that are valid TimeRange values
    const validPresets = allowedPresets.filter(preset => 
      ['7d', '30d', '90d', 'lifetime'].includes(preset)
    ) as TimeRange[];
    
    return validPresets.map(preset => {
      switch (preset) {
        case '7d':
          return {
            value: '7d',
            label: t('metaDashboard.common.last7days', 'Last 7 days'),
            description: 'Metrics from the last 7 days'
          };
        case '30d':
          return {
            value: '30d',
            label: t('metaDashboard.common.last30days'),
            description: 'Metrics from the last 30 days'
          };
        case '90d':
          return {
            value: '90d',
            label: t('metaDashboard.common.last90days'),
            description: 'Metrics from the last 90 days'
          };
        case 'lifetime':
          return {
            value: 'lifetime',
            label: t('metaDashboard.common.lifetime'),
            description: 'All-time metrics for your account'
          };
        default:
          return {
            value: preset,
            label: preset,
            description: `Metrics from ${preset} period`
          };
      }
    });
  }, [page?.platform, t]);

  // Effect to handle platform changes and invalid time range selections
  React.useEffect(() => {
    if (timeRangeOptions.length > 0) {
      const isCurrentSelectionValid = timeRangeOptions.some(option => option.value === selectedTimeRange);
      if (!isCurrentSelectionValid) {
        // If current selection is invalid, select the first available option
        const newTimeRange = timeRangeOptions[0].value;
        logger.info(`Selected time range ${selectedTimeRange} not available for ${page?.platform}, switching to ${newTimeRange}`);
        setSelectedTimeRange(newTimeRange);
      }
    }
  }, [timeRangeOptions, selectedTimeRange, page?.platform]);

  // Handle time range change
  const handleTimeRangeChange = (
    _event: React.MouseEvent<HTMLElement>,
    newTimeRange: TimeRange | null
  ) => {
    if (newTimeRange !== null && newTimeRange !== selectedTimeRange) {
      logger.info(`Time range changed to: ${newTimeRange}`);
      setSelectedTimeRange(newTimeRange);
      // Data will be re-fetched automatically when selectedTimeRange changes via useEffect
      // This will make different API calls based on the selected time range:
      // - 30d: Instagram API with 28-day limit, Facebook API with 30-day data
      // - 90d: Instagram API with 28-day limit, Facebook API with 90-day data
      // - lifetime: Instagram API with 28-day limit, Facebook API with lifetime data
    }
  };

  // Updated metricsData state to include all Instagram metrics with correct labeling
  interface PageMetricsData {
    impressions: { total: number };  // Instagram: Views/Impressions
    reach: { total: number };        // Instagram: Actual reach
    engagement: { total: number };
    followers: { total: number };
    profile_views: { total: number }; // Instagram: Profile views
    saved: { total: number };        // Instagram: Saved posts
    media_count: { total: number };  // Instagram: Published posts
    lowDataWarning?: boolean;
  }
  const [historicalData, setHistoricalData] = useState<HistoricalMetrics | null>(null);
  const [, setLoadingHistorical] = useState<boolean>(false);
  const [metricsData, setMetricsData] = useState<PageMetricsData>({
    impressions: { total: 0 },
    reach: { total: 0 },
    engagement: { total: 0 },
    followers: { total: 0 },
    profile_views: { total: 0 },
    saved: { total: 0 },
    media_count: { total: 0 },
    lowDataWarning: false
  });

  // Helper function for metric descriptions (to be translated)
  const getMetricDescription = (metricKey: string): string => {
    const isInstagram = page?.platform === 'instagram';
    const isLifetime = selectedTimeRange === 'lifetime';
    
    // Add time range context to descriptions
    const timeRangeContext = isLifetime ? 
      ' (Lifetime total)' : 
      ` (${selectedTimeRange} period)`;
    
    let baseDescription = '';
    switch (metricKey) {
      case 'impressions':
        baseDescription = t(isInstagram ? 'metaDashboard.overview.impressionsDesc' : 'metaDashboard.overview.impressionsDescFB');
        break;
      case 'reach':
        baseDescription = t(isInstagram ? 'metaDashboard.overview.reachDesc' : 'metaDashboard.overview.reachDescFB');
        break;
      case 'engagement':
        baseDescription = t(isInstagram ? 'metaDashboard.overview.engagementDescIG' : 'metaDashboard.overview.engagementDescFB');
        break;
      case 'followers':
        // Followers is always a current/lifetime metric regardless of time range
        return t(isInstagram ? 'metaDashboard.overview.followersDesc' : 'metaDashboard.overview.pageLikesDesc') + ' (Current total)';
      case 'profile_views':
        baseDescription = t(isInstagram ? 'metaDashboard.overview.profileViewsDesc' : 'metaDashboard.overview.pageViewsDescFB');
        break;
      case 'saved':
        baseDescription = t('metaDashboard.overview.savedDesc');
        break;
      case 'media_count':
        // Media count is always a current/lifetime metric regardless of time range
        return t('metaDashboard.overview.mediaCountDesc') + ' (Current total)';
      default:
        return '';
    }
    
    return baseDescription + timeRangeContext;
  };



  // Function to fetch historical data for charts
  const fetchHistoricalData = useCallback(async (page: MetaPage) => {
    if (!user?.id_store || !page.id) return;
    
    setLoadingHistorical(true);
    try {
      // Use the actual selected time range for historical data
      const days = selectedTimeRange === 'lifetime' ? 365 : // 1 year for lifetime
                   selectedTimeRange === '90d' ? 90 : 
                   selectedTimeRange === '30d' ? 30 : 30;
      let historical: HistoricalMetrics;
      
      // Get current metrics to pass to historical service
      const currentMetrics = {
        impressions: metricsData.impressions.total,
        reach: metricsData.reach.total,
        engagement: metricsData.engagement.total,
        followers: metricsData.followers.total,
        profile_views: metricsData.profile_views.total,
        saved: metricsData.saved.total
      };
      
      if (page.platform === 'instagram') {
        historical = await metaHistoricalService.getInstagramHistoricalData(
          user.id_store,
          page.id,
          days
        );
      } else {
        historical = await metaHistoricalService.getFacebookHistoricalData(
          user.id_store,
          page.id,
          days,
          currentMetrics
        );
      }
      
      setHistoricalData(historical);
      logger.info('Historical data loaded successfully:', {
        platform: page.platform,
        timeRange: selectedTimeRange,
        dataPoints: historical.impressions.length
      });
    } catch (error) {
      logger.error('Failed to load historical data:', error);
      setHistoricalData(null);
    } finally {
      setLoadingHistorical(false);
    }
  }, [user, selectedTimeRange, metricsData]);

  const fetchAllMetrics = useCallback(async (page: MetaPage) => {
    logger.info('fetchAllMetrics called for page:', page.id, 'with time range:', selectedTimeRange);
    
    // Skip the time check - we want to allow immediate fetches when time range changes
    // This check was preventing time range changes from fetching new data
    
    setIsFetching(true);

    if (!page || !page.id) return;
    
    try {
      setLoading(true);
      setError(null);
      setAuthError(false);

      // For Facebook, date range is handled by backend endpoint
      let impressions = 0, reach = 0, engagement = 0, followers = 0, profile_views = 0, saved = 0, media_count = 0;
      const failedMetrics: string[] = [];
      const dataSources: string[] = [];

    // Facebook - Use media-level insights API
    if (page.platform === 'facebook') {
      logger.info('Starting Facebook metrics fetch for page:', page.id);
      
      try {
        // Use the Facebook media-level insights API
        console.log('📊 Using Facebook media-level insights API');
        const data = await httpClient.get<{
          impressions?: number;
          reach?: number;
          engagement?: number;
          followers?: number;
          profile_views?: number;
          saved?: number;
          media_count?: number;
          // Additional Facebook-specific properties
          engaged_users?: number;
          post_engagements?: number;
          page_likes?: number;
          page_views?: number;
          posts_count?: number;
          source?: string;
          message?: string;
          error?: string;
        }>(
          `/api/meta/${user?.id_store}/facebook-media-insights?page_id=${page.id}&time_range=${selectedTimeRange}`
        );
        console.log('📊 Facebook metrics received:', data);
        
        impressions = data.impressions || 0;
        reach = data.reach || 0;
        engagement = data.engaged_users || data.post_engagements || 0;
        followers = data.page_likes || page.fan_count || 0;
        profile_views = data.page_views || 0;
        // For Facebook, we don't have saved posts, so keep it 0
        saved = 0;
        media_count = data.posts_count || 0;
        
        dataSources.push('facebook_media:sdk');
        logger.info('Successfully fetched Facebook metrics via media-level API:', {
          impressions, reach, engagement, followers, profile_views, media_count
        });
      } catch (error) {
        logger.error('Facebook media-level API failed:', error);
        failedMetrics.push('facebook_media');
        dataSources.push('facebook_media:error');
        // Set all to 0 on error
        impressions = reach = engagement = followers = profile_views = saved = media_count = 0;
      }
    }
    
    // Instagram - Fetch all metrics from insights endpoint (cache-first approach)
    if (page.platform === 'instagram') {
      // Helper function to fetch all Instagram insights with cache-first approach
      const fetchInstagramInsights = async () => {
        if (!user?.id_store) {
          throw new Error('Store ID required for Instagram insights');
        }
        
        // Hybrid approach:
        // 7d/30d/90d: User-level API, lifetime: MongoDB-first then media-level API fallback
        logger.info(`Fetching Instagram data for time range: ${selectedTimeRange} (cache_first: ${selectedTimeRange === 'lifetime'})...`);
        
        // Use cache_first=true only for lifetime queries to check MongoDB first
        const useCache = selectedTimeRange === 'lifetime' ? 'true' : 'false';
        const data = await httpClient.get<{
          insights?: {
            impressions?: number;
            views?: number;
            reach?: number;
            engagement?: number;
            profile_views?: number;
            website_clicks?: number;
            saved?: number;
            media_count?: number;
          };
          source?: string;
          message?: string;
          error?: string;
        }>(
          `/api/instagram/${user.id_store}/insights?page_id=${page.id || 'auto'}&time_range=${selectedTimeRange}&cache_first=${useCache}`
        );
        console.log('📊 Instagram metrics received:', data);
        
        const insights = data.insights || {};
        return {
          impressions: insights.impressions || insights.views || 0,
          reach: insights.reach || 0,
          engagement: insights.engagement || 0,
          profile_views: insights.profile_views || 0,
          website_clicks: insights.website_clicks || 0,
          saved: insights.saved || 0,
          media_count: insights.media_count || 0
        };
      };

      try {
        logger.info('Fetching Instagram insights...');
        const insights = await fetchInstagramInsights();
        if (insights) {
          impressions = insights.impressions || 0;
          reach = insights.reach || 0;
          profile_views = insights.profile_views || 0;
          engagement = insights.engagement || 0;
          saved = insights.saved || 0;
          media_count = insights.media_count || 0;
          dataSources.push('instagram_metrics:sdk');
          logger.info('Successfully fetched Instagram metrics:', {
            impressions, reach, profile_views, engagement, saved, media_count
          });
        } else {
          // If insights failed, set all to 0 and mark as failed
          impressions = reach = profile_views = engagement = saved = media_count = 0;
          failedMetrics.push('instagram_metrics');
          dataSources.push('instagram_metrics:error');
          logger.warn('Instagram insights returned null');
        }
      } catch (error) {
        logger.error('Instagram insights failed:', error);
        // Set all metrics to 0 and mark as failed
        impressions = reach = profile_views = engagement = saved = media_count = 0;
        failedMetrics.push('instagram_metrics');
        dataSources.push('instagram_metrics:error');
        logger.error('Instagram insights failed completely, setting all to 0');
      }

      // Get followers from profile endpoint (separate from insights)
      try {
        if (!user?.id_store) {
          throw new Error('Store ID required for Instagram profile');
        }
        const profileData = await httpClient.get<{
          data?: {
            followers_count?: number;
            id?: string;
            username?: string;
            name?: string;
            biography?: string;
            follows_count?: number;
            media_count?: number;
            profile_picture_url?: string;
            website?: string;
          };
          error?: string;
        }>(
          `/api/instagram/${user.id_store}/profile${page.id && page.id !== 'auto' ? `?page_id=${page.id}` : ''}`
        );
        
        if (profileData.data && !profileData.error) {
          followers = profileData.data.followers_count || page.followers_count || 0;
          dataSources.push('followers:sdk');
        } else {
          followers = page.followers_count || 0;
          dataSources.push('followers:fallback');
        }
      } catch (error) {
        logger.error('Instagram profile fetch failed:', error);
        followers = page.followers_count || 0;
        failedMetrics.push('followers');
        dataSources.push('followers:error');
      }
    }

    // Log data sources for debugging
    logger.info(`Meta Overview Panel data sources: ${dataSources.join(', ')}`);
    
    // Show data source info to user when using fallback
    const hasMongoDBData = dataSources.some(source => source.includes(':mongodb'));
    if (hasMongoDBData) {
      logger.info('Some metrics loaded from MongoDB cache due to API limitations');
    }

    logger.info('Setting metrics data with values:', {
      impressions, reach, engagement, followers, profile_views, saved, media_count
    });
    
    setMetricsData({
      impressions: { total: impressions },
      reach: { total: reach },
      engagement: { total: engagement },
      followers: { total: followers },
      profile_views: { total: profile_views },
      saved: { total: saved },
      media_count: { total: media_count },
      lowDataWarning: false
    });

    // Provide feedback about data sources and potential issues
    if (failedMetrics.length === 4) {
      setAuthError(true);
      setError('Authentication failed. Please reconnect your Meta account.');
    } else if (failedMetrics.length > 0) {
      const hasSDKData = dataSources.some(source => source.includes(':sdk'));
      const hasMongoDBData = dataSources.some(source => source.includes(':mongodb'));
      
      if (hasMongoDBData && !hasSDKData) {
        setError(`Showing cached data only. Some live metrics could not be loaded: ${failedMetrics.join(', ')}. This may be due to Meta API rate limits or permissions. Data will auto-refresh when API access is restored.`);
      } else if (hasSDKData && hasMongoDBData) {
        setError(`Mixed data sources: Live data for some metrics, cached data for others (${failedMetrics.join(', ')}). This may be due to temporary API limitations.`);
      } else {
        setError(`Some metrics could not be loaded: ${failedMetrics.join(', ')}. Please check your Meta account permissions and connection.`);
      }
    } else {
      // Show success message about data sources
      const hasSDKData = dataSources.some(source => source.includes(':sdk'));
      const hasMongoDBData = dataSources.some(source => source.includes(':mongodb'));
      
      if (hasSDKData && !hasMongoDBData) {
        logger.info('All metrics loaded from live Meta APIs (SDK/direct API calls)');
      } else if (hasMongoDBData) {
        logger.info('Some metrics loaded from MongoDB cache for optimal performance');
      }
    }
    } catch (error) {
      logger.error('Fatal error in fetchAllMetrics:', error);
      setError('Failed to load metrics. Please try again.');
    } finally {
      // Always set loading to false
      setLoading(false);
      setIsFetching(false);
    }
  }, [user, selectedTimeRange]);

  // Check authentication status when component mounts
  useEffect(() => {
    const checkAuth = () => {
      const authStatus = isMetaAuthReady();
      logger.debug('Meta authentication status:', authStatus);
      setIsAuthenticated(authStatus);
      
      // If not authenticated, stop loading to show auth UI
      if (!authStatus) {
        setLoading(false);
      }
      
      return authStatus;
    };

    // Initial check
    checkAuth();

    // Listen for auth state changes
    const handleAuthChange = () => {
      if (checkAuth()) {
        // Just update auth state, data will be fetched by the other useEffect
        logger.debug('Meta auth state changed, authenticated:', checkAuth());
      } else {
        // Not authenticated, stop loading
        setLoading(false);
      }
    };

    document.addEventListener('meta-auth-state-change', handleAuthChange);
    
    return () => {
      document.removeEventListener('meta-auth-state-change', handleAuthChange);
    };
  }, []); // No dependencies needed for auth listener

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, []);

  // Fetch data when authentication, page, or time range changes
  useEffect(() => {
    if (!page) {
      return;
    }

    // For initial load, we need BOTH page and authentication
    const needsInitialFetch = !hasInitialFetchedRef.current && isAuthenticated;
    
    // Check if page or timeRange actually changed
    const pageChanged = previousPageIdRef.current !== undefined && previousPageIdRef.current !== page.id;
    const timeRangeChanged = previousTimeRangeRef.current !== selectedTimeRange && hasInitialFetchedRef.current;
    
    // Proceed if we need initial fetch OR if something changed
    if (!needsInitialFetch && !pageChanged && !timeRangeChanged) {
      logger.debug('No changes detected, skipping fetch');
      return;
    }
    
    // Log what changed
    if (pageChanged) logger.info(`Page changed from ${previousPageIdRef.current} to ${page.id}`);
    if (timeRangeChanged) logger.info(`Time range changed from ${previousTimeRangeRef.current} to ${selectedTimeRange}`);
    
    // Skip if not authenticated
    if (!isAuthenticated) {
      return;
    }
    
    // Skip if already fetching UNLESS the time range changed
    if (isFetching && !timeRangeChanged) {
      return;
    }
    
    // If time range changed while fetching, reset the fetching flag
    if (timeRangeChanged && isFetching) {
      setIsFetching(false);
    }
    
    // Update refs
    previousPageIdRef.current = page.id;
    previousTimeRangeRef.current = selectedTimeRange;
    hasInitialFetchedRef.current = true;
    
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current);
    }
    
    // Execute fetch with short delay
    fetchTimeoutRef.current = setTimeout(() => {
      logger.info('Starting data fetch for page:', page.id, 'with time range:', selectedTimeRange);
      fetchAllMetrics(page);
      fetchHistoricalData(page);
    }, 50);
    
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current);
      }
    };
  }, [page?.id, page, isAuthenticated, selectedTimeRange, isFetching, fetchAllMetrics, fetchHistoricalData]); // Depend on all relevant state

  // Metrics card renderer
  const MetricCard = ({ 
    title, 
    value, 
    description 
  }: { 
    title: string; 
    value: number; 
    description: string;
  }) => (
    <Paper elevation={0} sx={{ p: 2, height: '100%', border: '1px solid', borderColor: 'divider' }}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {title}
      </Typography>
      <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 1 }}>
        {formatNumber(value)}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {description}
      </Typography>
    </Paper>
  );

  // Debug log only in development and less frequently
  if (process.env.NODE_ENV === 'development') {
    logger.debug('MetaOverviewPanel render state:', {
      isAuthenticated,
      loading,
      hasData: Object.entries(metricsData).filter(([k,]) => k !== 'lowDataWarning').some(([,v]) => v.total > 0),
      platform: page?.platform
    });
  }

  return (
    <Card variant="outlined" sx={{ mt: 2 }}>
      <CardContent>
        <Typography variant="h6" component="div" gutterBottom>
          {t(page?.platform === 'instagram' ? 'metaDashboard.overview.instagramTitle' : 'metaDashboard.overview.pageTitle')}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          {t(page?.platform === 'instagram' ? 'metaDashboard.overview.instagramSubtitle' : 'metaDashboard.overview.pageSubtitle')}
        </Typography>

        {/* Time Range Selector */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            {t('metaDashboard.common.timeRange', 'Time Range')}
          </Typography>
          <ToggleButtonGroup
            value={selectedTimeRange}
            exclusive
            onChange={handleTimeRangeChange}
            aria-label="time range selection"
            size="small"
          >
            {timeRangeOptions.map((option) => (
              <ToggleButton 
                key={option.value} 
                value={option.value}
                title={option.description}
              >
                {option.label}
              </ToggleButton>
            ))}
          </ToggleButtonGroup>
        </Box>

        {!isAuthenticated ? (
          <MetaAuthRequired 
            onLoginSuccess={() => {
              setIsAuthenticated(true);
              if (page) fetchAllMetrics(page);
            }}
          />
        ) : loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Box>
            <Alert 
              severity={authError ? "warning" : "error"}
              sx={{ my: 2 }}
            >
              {error}
            </Alert>
            {authError && (
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <Typography variant="body2" color="text.secondary">
                  {t('metaDashboard.overview.reconnectPrompt', 'Please reconnect your Facebook account to view metrics')}
                </Typography>
              </Box>
            )}
          </Box>
        ) : (
          <>
            {/* Show warning if data is low */}
            {metricsData.lowDataWarning && (
              <Alert severity="info" sx={{ my: 2 }}>
                {t('metaDashboard.overview.lowDataWarning')}
              </Alert>
            )}
            
            {/* Show auth warning if there's an auth error */}
            {authError && error && (
              <Alert severity="warning" sx={{ my: 2 }}>
                {error}
              </Alert>
            )}
            
            {/* Metrics Cards */}
            <Grid container spacing={3} sx={{ mt: 1 }}>
              {page?.platform === 'instagram' ? (
                // Instagram: 6 metrics in 3x2 grid
                <>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.impressionsTitle')} 
                      value={metricsData.impressions.total} 
                      description={getMetricDescription('impressions')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.reachTitle')} 
                      value={metricsData.reach.total} 
                      description={getMetricDescription('reach')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.engagementTitle')} 
                      value={metricsData.engagement.total} 
                      description={getMetricDescription('engagement')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.followersTitle')} 
                      value={metricsData.followers.total} 
                      description={getMetricDescription('followers')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.profileViewsTitle')} 
                      value={metricsData.profile_views.total} 
                      description={getMetricDescription('profile_views')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <MetricCard 
                      title={t('metaDashboard.overview.savedTitle')} 
                      value={metricsData.saved.total} 
                      description={getMetricDescription('saved')}
                    />
                  </Grid>
                </>
              ) : (
                // Facebook: 4 metrics in 4x1 grid
                <>
                  <Grid item xs={12} sm={6} md={3}>
                    <MetricCard 
                      title={t('metaDashboard.overview.impressionsTitle')} 
                      value={metricsData.impressions.total} 
                      description={getMetricDescription('impressions')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <MetricCard 
                      title={t('metaDashboard.overview.engagementTitle')} 
                      value={metricsData.engagement.total} 
                      description={getMetricDescription('engagement')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <MetricCard 
                      title={t('metaDashboard.overview.pageLikesTitle')} 
                      value={metricsData.followers.total} 
                      description={getMetricDescription('followers')}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <MetricCard 
                      title={t('metaDashboard.overview.pageViewsTitle')} 
                      value={metricsData.profile_views.total} 
                      description={getMetricDescription('profile_views')}
                    />
                  </Grid>
                </>
              )}
            </Grid>

        {/* Metric Charts */}
        <Grid container spacing={3} sx={{ mt: 2 }}>
          {page?.platform === 'facebook' ? (
            // Facebook: 4 charts in 2x2 grid
            <>
              <Grid item xs={12} md={6}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.impressionsTitle')}
                  data={historicalData?.impressions || []}
                  chartType="line"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.engagementTitle')}
                  data={historicalData?.engagement || []}
                  chartType="bar"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
                <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                  {t('metaDashboard.common.actualLikesCommentsFootnote', 'Includes likes, comments, and shares')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.pageLikesTitle')}
                  data={historicalData?.followers || []}
                  chartType="line"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
                <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                  {t('metaDashboard.common.currentFollowerFootnote', 'Current number of people who like your Page')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.pageViewsTitle')}
                  data={historicalData?.profile_views || []}
                  chartType="bar"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
            </>
          ) : (
            // Instagram: 6 charts in 3x2 grid
            <>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.impressionsTitle')}
                  data={historicalData?.impressions || []}
                  chartType="line"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.reachTitle')}
                  data={historicalData?.reach || []}
                  chartType="line"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
                <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                  {t('metaDashboard.common.derivedReachFootnote', 'Unique accounts reached by your content')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.engagementTitle')}
                  data={historicalData?.engagement || []}
                  chartType="bar"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.followersTitle')}
                  data={historicalData?.followers || []}
                  chartType="line"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
                <Typography variant="caption" display="block" sx={{ mt: 1, color: 'text.secondary' }}>
                  {t('metaDashboard.common.currentFollowerFootnote', 'Current follower count')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.profileViewsTitle')}
                  data={historicalData?.profile_views || []}
                  chartType="bar"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <MetaInsightChart 
                  title={t('metaDashboard.overview.savedTitle')}
                  data={historicalData?.saved || []}
                  chartType="bar"
                  showTrend={true}
                  formatValue={(value) => formatNumber(value)}
                />
              </Grid>
            </>
          )}
        </Grid>
        </>
        )}
      </CardContent>
    </Card>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const MetaOverviewPanel = React.memo(MetaOverviewPanelComponent, (prevProps, nextProps) => {
  // Custom comparison function to prevent re-renders when props haven't changed
  return prevProps.page?.id === nextProps.page?.id && 
         prevProps.page?.platform === nextProps.page?.platform;
}); 