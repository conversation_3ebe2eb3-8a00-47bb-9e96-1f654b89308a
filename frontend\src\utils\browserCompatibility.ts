import { logger as _logger } from './logger';

export interface BrowserCompatibility {
  supportsThirdPartyCookies: boolean;
  hasStrictPrivacySettings: boolean;
  requiresSpecialHandling: boolean;
  browserName: string;
}

export const detectBrowserCompatibility = (): BrowserCompatibility => {
  const userAgent = navigator.userAgent.toLowerCase();
  
  // Detect browser types
  const isFirefox = userAgent.includes('firefox');
  const isSafari = userAgent.includes('safari') && !userAgent.includes('chrome');
  const isChrome = userAgent.includes('chrome') && !userAgent.includes('edge');
  const isEdge = userAgent.includes('edge');
  const isPrivacyBrowser = userAgent.includes('duckduckgo') || userAgent.includes('brave');
  
  // Check for third-party cookie support
  // This is a simplified check - in reality, this would require more sophisticated testing
  const supportsThirdPartyCookies = !isSafari && !isPrivacyBrowser;
  
  // Safari and Firefox have stricter default privacy settings
  const hasStrictPrivacySettings = isSafari || isFirefox || isPrivacyBrowser;
  
  const browserName = isFirefox ? 'Firefox' : 
                     isSafari ? 'Safari' : 
                     isChrome ? 'Chrome' : 
                     isEdge ? 'Edge' : 
                     isPrivacyBrowser ? 'Privacy Browser' : 'Unknown';
  
  return {
    supportsThirdPartyCookies,
    hasStrictPrivacySettings,
    requiresSpecialHandling: hasStrictPrivacySettings || !supportsThirdPartyCookies,
    browserName
  };
};
