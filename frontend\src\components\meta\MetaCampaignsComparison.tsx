import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  Box, 
  Card, 
  CardContent, 
  Typography, 
  CircularProgress, 
  Grid,
  Paper,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import { 
  MetaPage,
  TimeRange,
  MetaAdCampaign,
  MetaCampaignBasic,
  StoreSalesData,
  MetaAdAccount
} from '../../services/types';
import { MetaStoreService } from '../../services/metaStoreService';
import { TimeRangeFilter } from '../common/TimeRangeFilter';
import { getMetaTimeRangePresets, getDefaultMetaTimeRange } from '../../services/metaTimeRanges';
import { 
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine,
  Line,
  ComposedChart
} from 'recharts';
import { MetaDataService } from '../../services/dataService';
import { isMetaAuthReady } from '../../services/authChecker';
import { fetchWithDeduplication } from '../../services/apiService';
import { API_URL } from '../../config/api';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from 'react-i18next';
import ChartContainer from '../common/ChartContainer';
import { logger } from '../../utils/logger';
import { MetaPermissionAlert } from './MetaPermissionAlert';
import { useMetaPermissions } from '../../contexts/MetaPermissionsContext';
// import { useSnackbar } from 'notistack'; // Keep this commented until notistack is installed and used
// import { useTheme } from '@mui/material/styles'; // Keep this commented until theme is used

// Define tooltip content type
interface ChartDataPoint {
  date: string;
  conversion_rate: number;
  sales_value: number;
  [key: string]: string | number;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{ 
    name?: string;
    value?: number;
    dataKey?: string;
    color?: string;
    payload?: ChartDataPoint;
  }>;
  label?: string;
}

interface MetaCampaignsComparisonProps {
  page?: MetaPage;
  useMockData?: boolean;
}

// Add the MockCampaign interface definition (Checklist Item 2)
interface MockCampaign {
  id: string;
  name: string;
  status: string;
  objective: string;
  budget: number;
  start_time: string;
  end_time: string;
  spend: number;
  impressions: number;
  clicks: number;
  conversions: number;
  conversion_rate: number;
  correlation_metric: number;
}

// Add mock campaigns data for fallback
const mockCampaigns: MockCampaign[] = [ // Apply MockCampaign type here too for consistency
  {
    id: 'mock_campaign_1',
    name: 'Mock Summer Campaign',
    status: 'ACTIVE',
    objective: 'CONVERSIONS',
    budget: 500,
    start_time: '2025-03-01T00:00:00Z',
    end_time: '2025-04-01T00:00:00Z',
    spend: 350,
    impressions: 25000,
    clicks: 1250,
    conversions: 75,
    conversion_rate: 0.06,
    correlation_metric: 0.72
  },
  {
    id: 'mock_campaign_2',
    name: 'Mock Spring Sale',
    status: 'COMPLETED',
    objective: 'MESSAGES',
    budget: 250,
    start_time: '2025-02-01T00:00:00Z',
    end_time: '2025-03-01T00:00:00Z',
    spend: 250,
    impressions: 18000,
    clicks: 820,
    conversions: 42,
    conversion_rate: 0.05,
    correlation_metric: 0.65
  }
];

/**
 * Utility function to convert MetaCampaignBasic to MetaAdCampaign
 */
const convertToFullCampaign = (basicCampaign: MetaCampaignBasic): MetaAdCampaign => {
  return {
    ...basicCampaign,
    objective: 'UNKNOWN', // Default value
    start_time: new Date().toISOString(), // Default value
    budget: basicCampaign.spend || 0, // Use spend as a fallback for budget
    conversion_rate: Math.random() * 0.05 + 0.01, // Random default as done in existing code
    correlation_metric: 0, // Fallback default
    correlation_data: [], // Fallback empty array
  };
};

/**
 * Generate enhanced correlation data using real sales data and campaign metrics
 */
const generateCorrelationData = (
  campaign: MetaAdCampaign, 
  salesData: StoreSalesData, 
  timeRange: TimeRange
): ChartDataPoint[] => {
  const startDate = new Date(timeRange.since);
  const endDate = new Date(timeRange.until);
  
  const result: ChartDataPoint[] = [];
  
  // Check if campaign has real correlation data from backend
  if (campaign.correlation_data && Array.isArray(campaign.correlation_data) && campaign.correlation_data.length > 0) {
    logger.debug(`Using real correlation data from backend for campaign ${campaign.id}:`, campaign.correlation_data);
    // Sort by date to ensure chronological order
    const sortedData = campaign.correlation_data
      .map(item => ({
        date: item.date,
        conversion_rate: item.conversion_rate || calculateRealConversionRate(campaign),
        sales_value: item.revenue || item.sales_value || item.sales || 0
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    logger.debug(`Processed real correlation data for campaign ${campaign.id}:`, sortedData);
    return sortedData;
  }
  
  // Get campaign actual date range (not the chart time range)
  const campaignStartDate = campaign.start_time ? new Date(campaign.start_time) : null;
  const campaignEndDate = campaign.end_time ? new Date(campaign.end_time) : null;
  
  // If no campaign dates, log warning and use minimal data
  if (!campaignStartDate || !campaignEndDate) {
    logger.warn(`Campaign ${campaign.id} missing start/end dates, showing minimal data`);
    return [{
      date: startDate.toISOString().split('T')[0],
      conversion_rate: 0.001,
      sales_value: 0
    }];
  }
  
  // Determine the actual date range to show (intersection of campaign period and chart range)
  const effectiveStartDate = new Date(Math.max(campaignStartDate.getTime(), startDate.getTime()));
  const effectiveEndDate = new Date(Math.min(campaignEndDate.getTime(), endDate.getTime()));
  
  // If campaign doesn't overlap with the chart time range, show no data
  if (effectiveStartDate > effectiveEndDate) {
    logger.info(`Campaign ${campaign.id} (${campaignStartDate.toISOString().split('T')[0]} to ${campaignEndDate.toISOString().split('T')[0]}) doesn't overlap with chart range`);
    return [];
  }
  
  // Generate data points only for the campaign period
  const effectiveDayDiff = Math.round((effectiveEndDate.getTime() - effectiveStartDate.getTime()) / (1000 * 60 * 60 * 24));
  
  for (let i = 0; i <= effectiveDayDiff; i++) {
    const currentDate = new Date(effectiveStartDate);
    currentDate.setDate(effectiveStartDate.getDate() + i);
    const dateStr = currentDate.toISOString().split('T')[0];
    
    // Only show sales data that actually happened during the campaign period
    const salesForDate = salesData.daily_data.find(item => item.date === dateStr);
    // IMPORTANT: Only show sales if they actually exist for this date AND we're in campaign period
    const actualSales = salesForDate ? (salesForDate.sales || salesForDate.revenue || 0) : 0;
    
    // Log for debugging - remove after fixing
    if (actualSales > 0) {
      logger.debug(`Campaign ${campaign.id}: Found actual sales on ${dateStr}: $${actualSales}`);
    }
    
    // Calculate realistic conversion rate based on campaign performance
    let conversionRate = 0;
    if (campaign.clicks && campaign.conversions && campaign.clicks > 0) {
      // Use real campaign data if available
      conversionRate = campaign.conversions / campaign.clicks;
    } else if (campaign.impressions && campaign.clicks && campaign.impressions > 0) {
      // Calculate CTR and estimate conversion rate
      const ctr = campaign.clicks / campaign.impressions;
      conversionRate = ctr * 0.02; // Assume 2% of clicks convert
    } else {
      // Fallback based on campaign objective
      switch (campaign.objective?.toUpperCase()) {
        case 'MESSAGES':
          conversionRate = 0.08; // 8% for message campaigns
          break;
        case 'CONVERSIONS':
          conversionRate = 0.025; // 2.5% for conversion campaigns
          break;
        case 'TRAFFIC':
          conversionRate = 0.015; // 1.5% for traffic campaigns
          break;
        default:
          conversionRate = 0.02; // 2% default
      }
    }
    
    result.push({
      date: dateStr,
      conversion_rate: Math.max(0.001, conversionRate),
      sales_value: actualSales
    });
  }
  
  // Ensure chronological order (earliest to latest)
  result.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  logger.debug(`Generated correlation data for campaign ${campaign.id}: ${result.length} days, ${result.filter(d => d.sales_value > 0).length} days with sales`);
  
  return result;
};

/**
 * Calculate real conversion rate based on campaign data
 */
const calculateRealConversionRate = (campaign: MetaAdCampaign): number => {
  // If campaign has real metrics, use them
  if (campaign.clicks && campaign.conversions && campaign.clicks > 0) {
    return campaign.conversions / campaign.clicks;
  }
  
  // If campaign has spend and cost per conversion, estimate
  if (campaign.spend && campaign.conversions && campaign.conversions > 0) {
    const costPerConversion = campaign.spend / campaign.conversions;
    // Lower cost per conversion = higher conversion rate
    return Math.min(0.1, Math.max(0.001, 1 / (costPerConversion * 10)));
  }
  
  // Fallback to campaign objective-based rates
  switch (campaign.objective?.toUpperCase()) {
    case 'CONVERSIONS':
      return 0.025; // 2.5% for conversion campaigns
    case 'MESSAGES':
      return 0.08;  // 8% for message campaigns
    case 'TRAFFIC':
      return 0.015; // 1.5% for traffic campaigns
    case 'ENGAGEMENT':
      return 0.05;  // 5% for engagement campaigns
    default:
      return 0.02;  // 2% default
  }
};

/**
 * Calculate attributed sales for a campaign using advanced attribution windows
 */
const calculateCampaignAttributedSales = (campaign: MetaAdCampaign, salesData: StoreSalesData): number => {
  if (!campaign.start_time || !campaign.end_time) {
    logger.debug(`Campaign ${campaign.id} missing date information for attribution`);
    return 0;
  }

  const campaignStart = new Date(campaign.start_time);
  const campaignEnd = new Date(campaign.end_time);
  
  // Define attribution windows (industry standard for social media campaigns)
  const clickThroughWindow = 7; // 7 day click-through attribution
  
  // Extended attribution period (campaign duration + attribution windows)
  const attributionStart = new Date(campaignStart);
  attributionStart.setDate(attributionStart.getDate() - 1); // Start 1 day before campaign
  
  const attributionEnd = new Date(campaignEnd);
  attributionEnd.setDate(attributionEnd.getDate() + clickThroughWindow); // Extend 7 days after campaign
  
  // Get baseline sales (30 days before campaign) for comparison
  const baselineStart = new Date(campaignStart);
  baselineStart.setDate(baselineStart.getDate() - 30);
  const baselineEnd = new Date(campaignStart);
  baselineEnd.setDate(baselineEnd.getDate() - 1);
  
  // Calculate baseline daily average
  const baselineSales = salesData.daily_data.filter(day => {
    const dayDate = new Date(day.date);
    return dayDate >= baselineStart && dayDate <= baselineEnd;
  });
  
  const baselineDailyAverage = baselineSales.length > 0 
    ? baselineSales.reduce((sum, day) => sum + (day.sales || day.revenue || 0), 0) / baselineSales.length
    : 0;
  
  // Calculate campaign period sales
  const campaignSales = salesData.daily_data.filter(day => {
    const dayDate = new Date(day.date);
    return dayDate >= attributionStart && dayDate <= attributionEnd;
  });
  
  const totalCampaignSales = campaignSales.reduce((sum, day) => sum + (day.sales || day.revenue || 0), 0);
  const campaignDays = campaignSales.length;
  
  // Calculate lift over baseline
  const expectedBaselineSales = baselineDailyAverage * campaignDays;
  const incrementalSales = Math.max(0, totalCampaignSales - expectedBaselineSales);
  
  // Apply attribution factor based on campaign performance
  let attributionFactor = 0.3; // Default 30% attribution
  
  // Adjust attribution based on campaign objective and performance
  if (campaign.objective) {
    switch (campaign.objective.toUpperCase()) {
      case 'CONVERSIONS':
        attributionFactor = 0.6; // Higher attribution for conversion campaigns
        break;
      case 'MESSAGES':
        attributionFactor = 0.4; // Medium attribution for message campaigns
        break;
      case 'TRAFFIC':
        attributionFactor = 0.35; // Medium-low attribution for traffic campaigns
        break;
      case 'ENGAGEMENT':
        attributionFactor = 0.25; // Lower attribution for engagement campaigns
        break;
    }
  }
  
  // Adjust attribution based on campaign spend/budget (higher spend = higher confidence)
  if (campaign.budget && campaign.budget > 0) {
    const budgetFactor = Math.min(1, campaign.budget / 1000); // Scale by budget (max factor at $1000)
    attributionFactor *= (0.8 + budgetFactor * 0.4); // Apply budget scaling (80%-120%)
  }
  
  // Calculate final attributed sales
  const attributedSales = incrementalSales * attributionFactor;
  
  logger.debug(`Campaign ${campaign.id} attribution:`, {
    baselineDailyAverage: baselineDailyAverage.toFixed(2),
    totalCampaignSales: totalCampaignSales.toFixed(2),
    expectedBaselineSales: expectedBaselineSales.toFixed(2),
    incrementalSales: incrementalSales.toFixed(2),
    attributionFactor: attributionFactor.toFixed(2),
    attributedSales: attributedSales.toFixed(2)
  });
  
  return Math.round(attributedSales);
};

/**
 * Component for displaying Meta campaign conversion rates compared with La Nube sales
 */
export const MetaCampaignsComparison: React.FC<MetaCampaignsComparisonProps> = ({
  page,
  useMockData = false
}) => {
  // const { enqueueSnackbar } = useSnackbar(); // Keep this commented until notistack is installed and used
  const { t } = useTranslation();
  // const theme = useTheme(); // Keep this commented until theme is used
  const { user } = useAuth();
  const { canAccessFeature } = useMetaPermissions();
  const storeIdFromAuth = user?.id_store?.toString();

  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [campaigns, setCampaigns] = useState<MetaAdCampaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string | null>(null);
  const [campaignTypes, setCampaignTypes] = useState<string[]>([]);
  const [selectedCampaignType, setSelectedCampaignType] = useState<string | null>(null);
  const [campaignAttributedSales, setCampaignAttributedSales] = useState<Record<string, number>>({});
  const [timeRange, setTimeRange] = useState<TimeRange | null>(getDefaultMetaTimeRange());
  const [comparisonData, setComparisonData] = useState<ChartDataPoint[]>([]);
  const [salesData, setSalesData] = useState<StoreSalesData | null>(null);

  // Define filteredCampaignsForDropdown using useMemo
  const filteredCampaignsForDropdown = useMemo(() => {
    if (!selectedCampaignType) {
      return campaigns || []; // Return all if no type selected, or an empty array
    }
    return (campaigns || []).filter(
      (campaign) => campaign.objective?.toUpperCase() === selectedCampaignType.toUpperCase()
    );
  }, [campaigns, selectedCampaignType]);

  // Create comparison data for charts and tables
  const createComparisonData = useCallback(async (campaign: MetaAdCampaign, salesData: StoreSalesData, timeRange: TimeRange | null): Promise<ChartDataPoint[]> => {
    // Add check for null timeRange at the beginning
    if (!timeRange) {
        logger.warn('Cannot create comparison data without a time range.');
        return []; 
    }
    
    // PRIORITY 1: If campaign has real correlation data from backend, use it directly
    if (campaign.correlation_data && Array.isArray(campaign.correlation_data) && campaign.correlation_data.length > 0) {
      logger.debug(`Using pre-loaded correlation data for campaign ${campaign.id} (${campaign.correlation_data.length} points)`);
      const correlationChartData = campaign.correlation_data
        .map(item => ({
          date: item.date,
          conversion_rate: item.conversion_rate || calculateRealConversionRate(campaign),
          sales_value: item.revenue || item.sales_value || item.sales || 0
        }))
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
      
      logger.debug(`Processed correlation data for campaign ${campaign.id}:`, correlationChartData);
      return correlationChartData;
    }
    
    // Try to fetch real correlation data from the API if not in mock mode
    if (!useMockData) {
      try {
        // Instead of using non-existent getCampaignComparisonData, use the getAdMetrics
        // and extract/transform the data as needed
        const adMetricsData = await MetaStoreService.getAdMetrics(page?.id || '', storeIdFromAuth || '', timeRange ?? undefined);
        
        // Check if we have campaign-specific data from the response
        if (adMetricsData?.campaigns) {
          const targetCampaign = adMetricsData.campaigns.find((c: MetaCampaignBasic | MetaAdCampaign) => c.id === campaign.id);
          if (targetCampaign) {
            // Since targetCampaign doesn't have correlation_data directly, generate it
            return generateCorrelationData(convertToFullCampaign(targetCampaign), salesData, timeRange);
          }
        }
        
        // No specific correlation data found, generate it locally
      } catch (error) {
        logger.warn('Error fetching correlation data from API, generating locally:', error);
        // Continue with local correlation generation
      }
    }
    
    // Generate correlation data locally
    return generateCorrelationData(campaign, salesData, timeRange);
    // Suppress Warning for timeRange usage within generateCorrelationData called within this callback
    // eslint-disable-next-line react-hooks/exhaustive-deps -- timeRange is used in generateCorrelationData called within this callback
  }, [page, timeRange, useMockData]);

  // Fetch campaign data and sales data
  useEffect(() => {
    if (!page || !storeIdFromAuth) {
        if (!storeIdFromAuth) {
            logger.warn('Store ID not available from authentication context. Cannot fetch data.');
            setError(t('settings.errorNoStoreId'));
        }
        return;
    }

    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        logger.debug(`Using store ID from context: ${storeIdFromAuth} and page ID: ${page.id}`);

        // Try to fetch real campaign sales correlation data first
        let campaignData: MetaAdCampaign[] = [];
        let fetchedSalesData: StoreSalesData | null = null;

        try {
          logger.debug('Fetching campaign data...');
          
          // Use correlation API only for Instagram pages, not Facebook
          const shouldUseCorrelationData = page?.platform === 'instagram';
          
          if (shouldUseCorrelationData) {
            // Try correlation API first for enhanced Instagram data
            try {
              const correlationData = await MetaStoreService.getCampaignSalesCorrelation(storeIdFromAuth, timeRange || undefined);
              
              if (correlationData.enhanced_campaigns && correlationData.enhanced_campaigns.length > 0) {
                logger.debug(`Successfully fetched ${correlationData.enhanced_campaigns.length} enhanced Instagram campaigns with correlation data`);
                logger.debug('Enhanced campaigns data:', correlationData.enhanced_campaigns);
                
                // Filter campaigns by selected type if needed
                let filteredCampaigns = correlationData.enhanced_campaigns;
                if (selectedCampaignType && selectedCampaignType !== 'ALL') {
                  filteredCampaigns = filteredCampaigns.filter(
                    campaign => campaign.objective?.toUpperCase() === selectedCampaignType?.toUpperCase()
                  );
                }
                
                // Log each campaign's correlation data
                filteredCampaigns.forEach(campaign => {
                  logger.debug(`Campaign ${campaign.id} (${campaign.name}) correlation data:`, campaign.correlation_data);
                });
                
                campaignData = filteredCampaigns;
                
                // Transform sales data to match expected format
                logger.debug('Raw sales data from correlation API:', correlationData.sales_data);
                const dailyData = correlationData.sales_data.daily_sales.map(day => ({
                  date: day.date,
                  sales: day.sales || day.revenue || 0,
                  orders: 1, // Default to 1 order if not available
                  revenue: day.revenue || day.sales || 0
                }));
                const totalSales = dailyData.reduce((sum, day) => sum + day.sales, 0);
                const totalOrders = dailyData.reduce((sum, day) => sum + day.orders, 0);
                
                fetchedSalesData = {
                  total_sales: totalSales,
                  order_count: totalOrders,
                  avg_order_value: totalOrders > 0 ? totalSales / totalOrders : 0,
                  daily_data: dailyData
                };
                logger.debug('Transformed sales data:', fetchedSalesData);
                
                logger.debug(`Using enhanced Instagram correlation data: ${campaignData.length} campaigns, ${fetchedSalesData?.daily_data?.length || 0} daily sales records`);
              } else {
                // If no campaigns found for current date range, try without date filter to get any available data
                logger.info('No campaigns found for current date range, trying to fetch all available correlation data...');
                const allCorrelationData = await MetaStoreService.getCampaignSalesCorrelation(storeIdFromAuth);
                
                if (allCorrelationData.enhanced_campaigns && allCorrelationData.enhanced_campaigns.length > 0) {
                  logger.info(`Found ${allCorrelationData.enhanced_campaigns.length} campaigns in total correlation data, using broader dataset`);
                  
                  // Filter campaigns by selected type if needed
                  let filteredCampaigns = allCorrelationData.enhanced_campaigns;
                  if (selectedCampaignType && selectedCampaignType !== 'ALL') {
                    filteredCampaigns = filteredCampaigns.filter(
                      campaign => campaign.objective?.toUpperCase() === selectedCampaignType?.toUpperCase()
                    );
                  }
                  
                  campaignData = filteredCampaigns;
                  
                  // Transform sales data to match expected format
                  const allDailyData = allCorrelationData.sales_data.daily_sales.map(day => ({
                    date: day.date,
                    sales: day.sales || day.revenue || 0,
                    orders: 1, // Default to 1 order if not available
                    revenue: day.revenue || day.sales || 0
                  }));
                  const allTotalSales = allDailyData.reduce((sum, day) => sum + day.sales, 0);
                  const allTotalOrders = allDailyData.reduce((sum, day) => sum + day.orders, 0);
                  
                  fetchedSalesData = {
                    total_sales: allTotalSales,
                    order_count: allTotalOrders,
                    avg_order_value: allTotalOrders > 0 ? allTotalSales / allTotalOrders : 0,
                    daily_data: allDailyData
                  };
                  
                  logger.info(`Using all available correlation data: ${campaignData.length} campaigns, ${fetchedSalesData?.daily_data?.length || 0} daily sales records`);
                } else {
                  throw new Error('No enhanced campaigns found in correlation data');
                }
              }
            } catch (correlationError) {
              logger.warn('Instagram correlation data not available, falling back to existing campaign fetch logic:', correlationError);
              
              // Try to trigger correlation update in background if it's missing data
              if (correlationError instanceof Error && correlationError.message.includes('not JSON serializable')) {
                logger.debug('Triggering correlation update due to serialization issue...');
                try {
                  await MetaStoreService.triggerSalesCorrelationUpdate(storeIdFromAuth);
                  logger.debug('Successfully triggered correlation update');
                } catch (updateError) {
                  logger.warn('Failed to trigger correlation update:', updateError);
                }
              }
              
              // Fallback to existing campaign and sales data fetching
            [campaignData, fetchedSalesData] = await Promise.all([
              // Fetch campaign data (existing logic - appears consistent)
              (async () => {
                let campaignsResult: (MetaCampaignBasic | MetaAdCampaign)[] = [];
                try {
                    const timeRangeObj = timeRange;
                    logger.debug(`Attempting to fetch campaign data with MetaStoreService using timeRange: ${JSON.stringify(timeRangeObj)}`);
                    const apiData = await MetaStoreService.getAdMetrics(page.id, storeIdFromAuth || '', timeRangeObj ?? undefined);
                    campaignsResult = apiData?.campaigns || [];
                                          logger.debug(`Retrieved ${campaignsResult.length} campaigns using MetaStoreService`);

                    if (campaignsResult.length === 0) {
                                                  logger.warn('Initial fetch returned no campaigns. Attempting fallback to fetch all campaigns from MongoDB...');
                        try {
                            const fallbackApiData = await MetaStoreService.getAdMetrics(page.id, storeIdFromAuth || '', undefined);
                            campaignsResult = fallbackApiData?.campaigns || [];
                                                          logger.debug(`Retrieved ${campaignsResult.length} campaigns using MongoDB fallback (no time range).`);
                            if (campaignsResult.length === 0) {
                                                                  logger.warn('MongoDB fallback also returned no campaigns.');
                            }
                        } catch (fallbackError) {
                                                          logger.error('Error during MongoDB fallback campaign fetch:', fallbackError);
                        }
                    }

                    if (campaignsResult.length === 0) {
                        try {
                            logger.debug('Attempting final fallback using MetaDataService direct API access');
                            const isAuthReady = await isMetaAuthReady();
                            if (!isAuthReady) {
                                                                  logger.warn('Meta authentication not ready, cannot use MetaDataService');
                                throw new Error('Meta authentication not available');
                            }
                            const timeRangeObj = timeRange;
                            if (page.platform === 'instagram') {
                                logger.debug('Fetching Instagram combined metrics directly from Meta API');
                                const instagramData = await MetaDataService.getInstagramCombinedMetrics(page.id, storeIdFromAuth || '', timeRangeObj ?? undefined);
                                if (instagramData && instagramData.campaigns && instagramData.campaigns.length > 0) {
                                                                          logger.debug(`Retrieved ${instagramData.campaigns.length} campaigns from direct Instagram API`);
                                    campaignsResult = instagramData.campaigns;
                                } else {
                                                                          logger.warn('No campaigns found in direct Instagram API response.');
                                }
                            } else {
                                                                  logger.debug('Fetching Facebook page ad accounts');
                                // First try to get the business ID from the page
                                const businessId = await MetaDataService.getBusinessIdFromPage(page.id);
                                let adAccounts: MetaAdAccount[] = [];
                                
                                if (businessId) {
                                    logger.debug(`Found business ID ${businessId} for page ${page.id}, fetching ad accounts`);
                                    adAccounts = await MetaDataService.getAdAccounts(businessId);
                                } else {
                                    logger.debug(`No business ID found for page ${page.id}, trying page-specific ad accounts`);
                                    // Fallback to the existing getFacebookPageAdAccounts method
                                    try {
                                        adAccounts = await MetaDataService.getAdAccounts(page.id);
                                    } catch (pageAdAccountError) {
                                        logger.warn('Page-specific ad account fetch also failed:', pageAdAccountError);
                                        adAccounts = [];
                                    }
                                }
                                if (!adAccounts || adAccounts.length === 0) {
                                                                          logger.warn('No ad accounts found for this page');
                                } else {
                                                                          logger.debug(`Found ${adAccounts.length} ad accounts for page ${page.id}`);
                                    const account = adAccounts[0];
                                    const campaigns = await MetaDataService.getAdCampaigns(account.id);
                                    if (campaigns && campaigns.length > 0) {
                                        logger.debug(`Retrieved ${campaigns.length} campaigns from account ${account.id}`);
                                        campaignsResult = campaigns as MetaAdCampaign[];
                                    } else {
                                                                                  logger.warn(`No campaigns found for account ${account.id}`);
                                    }
                                }
                            }
                        } catch (metaApiError) {
                            logger.error('Direct Meta API fallback also failed:', metaApiError);
                        }
                    }

                    if (campaignsResult.length === 0) {
                        logger.debug('All fetch attempts failed, using mock campaigns as fallback.');
                        campaignsResult = mockCampaigns.map((campaign: MockCampaign): MetaAdCampaign => ({
                            id: campaign.id,
                            name: campaign.name,
                            status: campaign.status,
                            objective: campaign.objective,
                            start_time: campaign.start_time,
                            end_time: campaign.end_time,
                            budget: campaign.budget,
                            daily_budget: undefined,
                            lifetime_budget: undefined,
                            conversion_rate: campaign.conversion_rate,
                            correlation_metric: campaign.correlation_metric,
                            correlation_data: [],
                        }));
                        localStorage.setItem('using_mock_data', 'true');
                    } else {
                        localStorage.removeItem('using_mock_data');
                    }

                    const mappedCampaigns: MetaAdCampaign[] = campaignsResult as MetaAdCampaign[];
                    return mappedCampaigns;

                } catch (campaignError) {
                    logger.error('Error fetching Meta campaigns:', campaignError);
                    setError(t('metaDashboard.campaigns.errorLoading', { errorDetails: (campaignError instanceof Error ? campaignError.message : String(campaignError)) }));
                    logger.debug('Campaign fetch failed, returning mapped mock data array.');
                    return mockCampaigns.map((campaign: MockCampaign): MetaAdCampaign => ({
                       id: campaign.id,
                       name: campaign.name,
                       status: campaign.status,
                       objective: campaign.objective,
                       start_time: campaign.start_time,
                       end_time: campaign.end_time,
                       budget: campaign.budget,
                       daily_budget: undefined,
                       lifetime_budget: undefined,
                       conversion_rate: campaign.conversion_rate,
                       correlation_metric: campaign.correlation_metric,
                       correlation_data: [],
                    }));
                }
            })(),
            // Fetch sales data
            (async () => {
              try {
                logger.debug(`Fetching sales data for store ${storeIdFromAuth} between ${timeRange?.since} and ${timeRange?.until}`);
                // Build the base URL for sales data
                const salesApiUrl = new URL(`${API_URL}/api/store/${storeIdFromAuth}/sales`);
                
                // Conditionally add 'since' parameter if timeRange.since exists
                if (timeRange?.since) {
                  salesApiUrl.searchParams.append('since', timeRange.since);
                }
                
                // Conditionally add 'until' parameter if timeRange.until exists
                if (timeRange?.until) {
                  salesApiUrl.searchParams.append('until', timeRange.until);
                }
                
                // Fetch sales data using the constructed URL
                const salesResponse = await fetchWithDeduplication<StoreSalesData>(
                  salesApiUrl.toString() // Use the safely constructed URL string
                );
                 logger.debug('Successfully fetched sales data:', salesResponse);
                 return salesResponse;
              } catch (salesError) {
                 logger.error('Error fetching sales data:', salesError);
                 return null;
              }
            })()
          ]);
          logger.debug('Finished fetching Instagram Campaign and Sales data using fallback method.');
          }
          } else {
            // Facebook pages use existing campaign fetch logic (no correlation data)
            logger.debug('Fetching Facebook campaign and sales data using traditional method...');
            [campaignData, fetchedSalesData] = await Promise.all([
              // Fetch Facebook campaign data (existing logic)
              (async () => {
                let campaignsResult: (MetaCampaignBasic | MetaAdCampaign)[] = [];
                try {
                    const timeRangeObj = timeRange;
                    logger.debug(`Attempting to fetch Facebook campaign data with MetaStoreService using timeRange: ${JSON.stringify(timeRangeObj)}`);
                    const apiData = await MetaStoreService.getAdMetrics(page.id, storeIdFromAuth || '', timeRangeObj ?? undefined);
                    campaignsResult = apiData?.campaigns || [];
                    logger.debug(`Retrieved ${campaignsResult.length} Facebook campaigns using MetaStoreService`);

                    if (campaignsResult.length === 0) {
                        logger.warn('Initial Facebook fetch returned no campaigns. Attempting fallback to fetch all campaigns from MongoDB...');
                        try {
                            const fallbackApiData = await MetaStoreService.getAdMetrics(page.id, storeIdFromAuth || '', undefined);
                            campaignsResult = fallbackApiData?.campaigns || [];
                            logger.debug(`Retrieved ${campaignsResult.length} Facebook campaigns using MongoDB fallback (no time range).`);
                            if (campaignsResult.length === 0) {
                                logger.warn('Facebook MongoDB fallback also returned no campaigns.');
                            }
                        } catch (fallbackError) {
                            logger.error('Error during Facebook MongoDB fallback campaign fetch:', fallbackError);
                        }
                    }

                    if (campaignsResult.length === 0) {
                        logger.debug('All Facebook fetch attempts failed, using mock campaigns as fallback.');
                        campaignsResult = mockCampaigns.map((campaign: MockCampaign): MetaAdCampaign => ({
                            id: campaign.id,
                            name: campaign.name,
                            status: campaign.status,
                            objective: campaign.objective,
                            start_time: campaign.start_time,
                            end_time: campaign.end_time,
                            budget: campaign.budget,
                            daily_budget: undefined,
                            lifetime_budget: undefined,
                            conversion_rate: campaign.conversion_rate,
                            correlation_metric: campaign.correlation_metric,
                            correlation_data: [],
                        }));
                        localStorage.setItem('using_mock_data', 'true');
                    } else {
                        localStorage.removeItem('using_mock_data');
                    }

                    const mappedCampaigns: MetaAdCampaign[] = campaignsResult as MetaAdCampaign[];
                    return mappedCampaigns;

                } catch (campaignError) {
                    logger.error('Error fetching Facebook Meta campaigns:', campaignError);
                    setError(t('metaDashboard.campaigns.errorLoading', { errorDetails: (campaignError instanceof Error ? campaignError.message : String(campaignError)) }));
                    logger.debug('Facebook campaign fetch failed, returning mapped mock data array.');
                    return mockCampaigns.map((campaign: MockCampaign): MetaAdCampaign => ({
                       id: campaign.id,
                       name: campaign.name,
                       status: campaign.status,
                       objective: campaign.objective,
                       start_time: campaign.start_time,
                       end_time: campaign.end_time,
                       budget: campaign.budget,
                       daily_budget: undefined,
                       lifetime_budget: undefined,
                       conversion_rate: campaign.conversion_rate,
                       correlation_metric: campaign.correlation_metric,
                       correlation_data: [],
                    }));
                }
            })(),
            // Fetch sales data for Facebook
            (async () => {
              try {
                logger.debug(`Fetching sales data for Facebook page for store ${storeIdFromAuth} between ${timeRange?.since} and ${timeRange?.until}`);
                const salesApiUrl = new URL(`${API_URL}/api/store/${storeIdFromAuth}/sales`);
                
                if (timeRange?.since) {
                  salesApiUrl.searchParams.append('since', timeRange.since);
                }
                
                if (timeRange?.until) {
                  salesApiUrl.searchParams.append('until', timeRange.until);
                }
                
                const salesResponse = await fetchWithDeduplication<StoreSalesData>(
                  salesApiUrl.toString()
                );
                 logger.debug('Successfully fetched Facebook sales data:', salesResponse);
                 return salesResponse;
              } catch (salesError) {
                 logger.error('Error fetching Facebook sales data:', salesError);
                 return null;
              }
            })()
          ]);
          logger.debug('Finished fetching Facebook Campaign and Sales data.');
          }
          
        } catch (fetchError) {
          logger.error('Error during data fetching:', fetchError);
          setError(t('metaDashboard.campaigns.errorLoading', { errorDetails: (fetchError instanceof Error ? fetchError.message : String(fetchError)) }));
          campaignData = [];
          fetchedSalesData = null;
        }

        // Process and set state (ensure this uses storeIdFromAuth if needed further down)
        const types = [...new Set(campaignData.map(campaign => campaign.objective || 'UNKNOWN'))];
        setCampaignTypes(types);

        if (types.length > 0 && !selectedCampaignType) {
          setSelectedCampaignType(types[0]);
        }

        setCampaigns(campaignData);
        setSalesData(fetchedSalesData);

        // Calculate real attributed sales for each campaign using correlation data
        if (fetchedSalesData && campaignData.length > 0) {
          logger.debug('Calculating real attributed sales for campaigns...');
          const attributedSales: Record<string, number> = {};
          
          campaignData.forEach((campaign) => {
            const attributedSalesValue = calculateCampaignAttributedSales(campaign, fetchedSalesData);
            attributedSales[campaign.id] = attributedSalesValue;
            logger.debug(`Attributed sales for campaign ${campaign.id}: $${attributedSalesValue}`);
          });
          
          setCampaignAttributedSales(attributedSales);
          logger.debug('Finished calculating attributed sales.');
        } else {
          logger.warn('Skipping attributed sales calculation - no sales data or campaigns.');
          setCampaignAttributedSales({}); // Clear any previous attributed sales
        }

      } catch (error) {
        logger.error('General error in fetchData:', error);
        setError(t('metaDashboard.campaigns.errorLoading', { errorDetails: (error instanceof Error ? error.message : String(error)) }));
        setCampaigns([]); // Clear data on error
        setSalesData(null);
        setCampaignAttributedSales({});
      } finally {
        setLoading(false);
      }
    };

    fetchData();

  }, [page, timeRange, selectedCampaignType, storeIdFromAuth, t]);

  // Update comparison data when selected campaign or sales data changes
  useEffect(() => {
    if (selectedCampaign && campaigns.length > 0 && salesData) {
      const updateComparisonData = async () => {
        const campaign = campaigns.find(c => c.id === selectedCampaign);
        if (campaign) {
          const comparison = await createComparisonData(campaign, salesData, timeRange);
          setComparisonData(comparison);
        } else if (useMockData && mockCampaigns.length > 0 && salesData && timeRange) {
          // Added this block to handle mock data display when no real campaign is selected yet
          const mockCampaignToDisplay = mockCampaigns[0]; 
          const fullMockCampaign = convertToFullCampaign(mockCampaignToDisplay);
          const comparison = await createComparisonData(fullMockCampaign, salesData, timeRange);
          setComparisonData(comparison);
        } else {
          // Optionally clear comparison data if no campaign or insufficient mock data
          // setComparisonData([]); 
        }
      };
      
      updateComparisonData();
    }
  }, [selectedCampaign, campaigns, salesData, createComparisonData, timeRange, useMockData]);

  // Handle campaign selection change
  const handleCampaignChange = (event: SelectChangeEvent<string>) => {
    setSelectedCampaign(event.target.value);
  };

  // Handle campaign type filter change
  const handleCampaignTypeChange = (event: SelectChangeEvent<string>) => {
    setSelectedCampaignType(event.target.value);
    setSelectedCampaign(null); // Reset selected campaign when type changes
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return `${(value * 100).toFixed(2)}%`;
  };

  // Calculate correlation coefficient between conversion rate and sales
  const calculateCorrelation = (data: ChartDataPoint[]): number => {
    if (!data || data.length < 2) return 0;
    
    const n = data.length;
    
    // Extract conversion rates and sales values
    const x = data.map(item => item.conversion_rate);
    const y = data.map(item => item.sales_value);
    
    // Calculate means
    const meanX = x.reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.reduce((sum, val) => sum + val, 0) / n;
    
    // Calculate covariance and variances
    let covariance = 0;
    let varianceX = 0;
    let varianceY = 0;
    
    for (let i = 0; i < n; i++) {
      const xDiff = x[i] - meanX;
      const yDiff = y[i] - meanY;
      covariance += xDiff * yDiff;
      varianceX += xDiff * xDiff;
      varianceY += yDiff * yDiff;
    }
    
    // Calculate correlation coefficient
    const correlation = varianceX && varianceY ? covariance / Math.sqrt(varianceX * varianceY) : 0;
    
    // Return correlation clamped between -1 and 1
    return Math.max(-1, Math.min(1, correlation));
  };

  // Calculate real business impact based on sales attribution and conversion performance
  const calculateRealBusinessImpact = (
    attributedSales: number, 
    conversionRate: number, 
    campaignSpend: number = 0,
    correlation: number = 0
  ): number => {
    // Weights for different impact factors
    const salesWeight = 0.6;        // 60% - Actual sales drive business value
    const conversionWeight = 0.2;   // 20% - Conversion efficiency matters  
    const roiWeight = 0.15;         // 15% - Return on investment
    const correlationWeight = 0.05; // 5% - Statistical correlation as minor factor
    
    // Calculate sales impact (0-100 scale)
    // Scale based on reasonable campaign expectations ($0-$5000 range)
    const salesImpact = Math.min((attributedSales / 5000) * 100, 100);
    
    // Calculate conversion rate impact (0-100 scale)
    // Scale conversion rate (0-20% range, where 10%+ is excellent)
    const conversionImpact = Math.min((conversionRate * 100) / 10 * 100, 100);
    
    // Calculate ROI impact (0-100 scale)
    let roiImpact = 0;
    if (campaignSpend > 0 && attributedSales > 0) {
      const roi = (attributedSales - campaignSpend) / campaignSpend;
      // Scale ROI: 100% ROI = 50 points, 200% ROI = 75 points, 400%+ ROI = 100 points
      roiImpact = Math.min(Math.max(roi * 25, 0), 100);
    } else if (attributedSales > 0 && campaignSpend === 0) {
      // Free traffic with sales = excellent ROI
      roiImpact = 90;
    }
    
    // Calculate correlation impact (0-100 scale)
    const correlationImpact = Math.abs(correlation) * 100;
    
    // Weighted final impact
    const totalImpact = 
      (salesImpact * salesWeight) + 
      (conversionImpact * conversionWeight) + 
      (roiImpact * roiWeight) + 
      (correlationImpact * correlationWeight);
    
    // Ensure impact is between 0-100
    const finalImpact = Math.max(0, Math.min(100, totalImpact));
    
    // Debug logging for transparency
    if (attributedSales > 0) {
      logger.debug(`Impact calculation: Sales=$${attributedSales}, CR=${(conversionRate*100).toFixed(1)}%, Spend=$${campaignSpend}, Final=${finalImpact.toFixed(1)}%`);
    }
    
    return finalImpact;
  };

  // Legacy function kept for backward compatibility
  const calculateImpactPercentage = (correlation: number): number => {
    // This is now deprecated in favor of calculateRealBusinessImpact
    return Math.abs(correlation) * 100;
  };
  
  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <Paper 
          elevation={3} 
          sx={{ 
            p: 1.5, 
            backgroundColor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography variant="subtitle2" sx={{ color: 'text.primary' }}>
            {label}
          </Typography>
          {payload.map((entry, index) => (
            <Box key={`tooltip-item-${index}`} sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <Box
                component="span"
                sx={{
                  width: 10,
                  height: 10,
                  borderRadius: '50%',
                  backgroundColor: entry.color,
                  display: 'inline-block',
                  mr: 1
                }}
              />
              <Typography variant="body2" sx={{ color: 'text.primary' }}>
                {entry.name === 'conversion_rate'
                  ? `Conversion Rate: ${formatPercentage(entry.value || 0)}`
                  : `Sales: ${formatCurrency(entry.value || 0)}`}
              </Typography>
            </Box>
          ))}
        </Paper>
      );
    }
    return null;
  };

  // Get campaign status color
  const getStatusColor = (status: string): "success" | "warning" | "info" | "error" | "default" => {
    switch (status.toUpperCase()) {
      case 'ACTIVE':
        return 'success';
      case 'PAUSED':
        return 'warning';
      case 'COMPLETED':
        return 'info';
      case 'DELETED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getTranslatedCampaignValue = (type: 'status' | 'objective', value: string): string => {
    if (!value) return '';
    const upperValue = value.toUpperCase();
    switch (type) {
      case 'status':
        if (upperValue === 'ACTIVE') return t('metaDashboard.campaigns.statusActive');
        if (upperValue === 'COMPLETED') return t('metaDashboard.campaigns.statusCompleted');
        if (upperValue === 'PAUSED') return t('metaDashboard.campaigns.statusPaused');
        if (upperValue === 'ARCHIVED') return t('metaDashboard.campaigns.statusArchived');
        break;
      case 'objective':
        if (upperValue === 'CONVERSIONS') return t('metaDashboard.campaigns.objectiveConversions');
        if (upperValue === 'MESSAGES') return t('metaDashboard.campaigns.objectiveMessages');
        if (upperValue === 'LINK CLICKS') return t('metaDashboard.campaigns.objectiveLinkClicks');
        if (upperValue === 'BRAND AWARENESS') return t('metaDashboard.campaigns.objectiveBrandAwareness');
        if (upperValue === 'REACH') return t('metaDashboard.campaigns.objectiveReach');
        if (upperValue === 'LEAD GENERATION') return t('metaDashboard.campaigns.objectiveLeadGeneration');
        break;
    }
    return value; // Fallback
  };

  const getCampaignDisplayName = (campaignInput: MetaAdCampaign | MockCampaign): string => {
    if (campaignInput.id === 'mock_campaign_1') return t('metaDashboard.campaigns.mockSummerCampaign');
    if (campaignInput.id === 'mock_campaign_2') return t('metaDashboard.campaigns.mockSpringSale');
    return campaignInput.name || t('metaDashboard.campaigns.defaultCampaignName', { id: campaignInput.id });
  };
  
  const getRelationship = (correlation: number): string => {
    if (correlation > 0.7) return t('metaDashboard.campaigns.relationshipStrongPositive');
    if (correlation > 0.3) return t('metaDashboard.campaigns.relationshipWeakPositive');
    if (correlation < -0.7) return t('metaDashboard.campaigns.relationshipStrongNegative');
    if (correlation < -0.3) return t('metaDashboard.campaigns.relationshipWeakNegative');
    return t('metaDashboard.campaigns.relationshipNeutral');
  };

  // Calculate correlation for the current data
  const correlation = calculateCorrelation(comparisonData);
  
  // Calculate overall impact using real business metrics for selected campaign
  const selectedCampaignData = campaigns.find(c => c.id === selectedCampaign);
  const selectedCampaignSales = selectedCampaignData ? (campaignAttributedSales[selectedCampaignData.id] || 0) : 0;
  const impactPercentage = selectedCampaignData 
    ? calculateRealBusinessImpact(
        selectedCampaignSales,
        selectedCampaignData.conversion_rate || 0,
        selectedCampaignData.spend || 0,
        correlation
      )
    : calculateImpactPercentage(correlation); // Fallback to old method if no campaign selected

  // Check permissions for campaigns comparison
  const platform = page?.platform || 'facebook';
  const permissionCheck = canAccessFeature('campaignsComparison', platform);
  const hasRequiredPermissions = permissionCheck.canAccess;

  return (
    <Card variant="outlined" sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 3 }}>
          <Typography variant="h6" component="div" gutterBottom>
            {t('metaDashboard.campaigns.title')}
          </Typography>
          <TimeRangeFilter 
            value={timeRange}
            onChange={setTimeRange}
            presets={getMetaTimeRangePresets(page?.platform || 'facebook', 'campaigns')}
            allowCustom={true}
          />
        </Box>

        {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
        
        {!hasRequiredPermissions && (
          <MetaPermissionAlert
            requiredPermissions={permissionCheck.missingPermissions}
            featureName={t('metaDashboard.campaigns.title')}
            onRetry={() => {
              setError(null);
              // Trigger a re-fetch of campaigns
              if (timeRange) {
                // This will be handled by the useEffect when permissions change
              }
            }}
          />
        )}
        
        {useMockData && (
          <Alert severity="info" sx={{ mb: 3 }}>
            {t('metaDashboard.campaigns.noCampaignData')}
          </Alert>
        )}
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>{t('metaDashboard.campaigns.loadingCampaigns')}</Typography>
          </Box>
        ) : (
          <>
            {campaigns.length === 0 ? (
              <Paper variant="outlined" sx={{ p: 3, textAlign: 'center' }}>
                <Typography variant="body1" color="text.secondary">
                  {t('metaDashboard.campaigns.noCampaignData')}
                </Typography>
              </Paper>
            ) : (
              <>
                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth size="small">
                      <InputLabel id="campaign-type-select-label">{t('metaDashboard.campaigns.labelCampaignType')}</InputLabel>
                      <Select
                        labelId="campaign-type-select-label"
                        id="campaign-type-select"
                        value={selectedCampaignType || ''}
                        label={t('metaDashboard.campaigns.labelCampaignType')}
                        onChange={handleCampaignTypeChange}
                        displayEmpty // Important for placeholder to show
                      >
                        <MenuItem value="" disabled>
                          <em>{t('metaDashboard.campaigns.placeholderCampaignType')}</em>
                        </MenuItem>
                        {campaignTypes.map((type) => (
                          <MenuItem key={type} value={type}>
                            {getTranslatedCampaignValue('objective', type) /* Already using helper */}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth size="small">
                      <Select
                        id="campaign-select"
                        value={selectedCampaign || ''}
                        onChange={handleCampaignChange}
                        displayEmpty // Important for placeholder to show
                      >
                        {!selectedCampaign && (
                          <MenuItem value="" disabled>
                            <em>
                              {/* Conditional placeholder based on if campaigns are loaded for the type */}
                              {filteredCampaignsForDropdown.length === 0 && selectedCampaignType 
                                ? t('metaDashboard.campaigns.noCampaignData') 
                                : t('metaDashboard.campaigns.placeholderSelectCampaign')}
                            </em>
                          </MenuItem>
                        )}
                        {filteredCampaignsForDropdown.map((campaign) => ( // Use filteredCampaignsForDropdown
                          <MenuItem key={campaign.id} value={campaign.id}>
                            {getCampaignDisplayName(campaign) /* Already using helper */}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                {selectedCampaign && (
                  <>
                    {/* Campaign Details */}
                    <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                      {campaigns.filter(c => c.id === selectedCampaign).map(campaign => (
                        <Grid container spacing={2} key={campaign.id}>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.tableHeaderCampaign')}</Typography>
                            <Typography variant="subtitle2">{getCampaignDisplayName(campaign)}</Typography>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelStatus')}</Typography>
                            <Chip 
                              label={getTranslatedCampaignValue('status', campaign.status)} 
                              size="small" 
                              color={getStatusColor(campaign.status)}
                            />
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelObjective')}</Typography>
                            <Typography variant="subtitle2">
                              {getTranslatedCampaignValue('objective', campaign.objective.toUpperCase())}
                            </Typography>
                          </Grid>
                          <Grid item xs={6} sm={3}>
                            <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelBudget')}</Typography>
                            <Typography variant="subtitle2">
                              {campaign.budget ? t('metaDashboard.campaigns.labelBudgetPerDay', {amount: formatCurrency(campaign.budget)}) : 'N/A'}
                            </Typography>
                          </Grid>
                        </Grid>
                      ))}
                    </Paper>

                    {/* Correlation Metrics */}
                    <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {t('metaDashboard.campaigns.labelConversionSalesCorrelation')}
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelCorrelationCoefficient')}</Typography>
                          <Typography variant="h6">
                            {correlation.toFixed(2)}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelImpactOnSales')}</Typography>
                          <Typography variant="h6">
                            {impactPercentage.toFixed(1)}%
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="body2" color="text.secondary">{t('metaDashboard.campaigns.labelRelationship')}</Typography>
                          <Typography variant="h6">
                            {getRelationship(correlation)}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>

                    {/* Comparison Chart */}
                    <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {t('metaDashboard.campaigns.chartTitleConversionVsSales')}
                      </Typography>
                      <Box sx={{ height: 400, mt: 3 }}>
                        {comparisonData.length === 0 && !loading && (
                           <Alert severity="info" sx={{ margin: 2 }}>
                             {t('metaDashboard.campaigns.noSalesData')}
                           </Alert>
                        )}
                        {comparisonData.length > 0 && (() => {
                          // Debug logging to see what data is being rendered
                          console.log('Chart data being rendered:', comparisonData);
                          console.log('First date:', comparisonData[0]?.date, 'Last date:', comparisonData[comparisonData.length - 1]?.date);
                          console.log('Sales values:', comparisonData.map(d => `${d.date}: $${d.sales_value}`));
                          
                          // Ensure data is sorted chronologically before rendering
                          const sortedData = [...comparisonData].sort((a, b) => 
                            new Date(a.date).getTime() - new Date(b.date).getTime()
                          );
                          
                          console.log('Sorted data first date:', sortedData[0]?.date, 'Sorted last date:', sortedData[sortedData.length - 1]?.date);
                          
                          return (
                        <ChartContainer width="100%" height="100%">
                          <ComposedChart
                            data={sortedData}
                            margin={{
                              top: 5,
                              right: 30,
                              left: 20,
                              bottom: 5,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis 
                              dataKey="date" 
                              type="category"
                              minTickGap={30}
                              tick={{ fontSize: 12 }}
                              tickFormatter={(value) => {
                                const date = new Date(value);
                                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                              }}
                              domain={['dataMin', 'dataMax']}
                            />
                            <YAxis 
                              yAxisId="left"
                              label={{ 
                                value: t('metaDashboard.campaigns.legendConversionRate'), 
                                angle: -90, 
                                position: 'insideLeft',
                                style: { textAnchor: 'middle' }
                              }}
                              tickFormatter={(value) => `${(value * 100).toFixed(1)}%`}
                              domain={[0, 'dataMax']}
                            />
                            <YAxis 
                              yAxisId="right" 
                              orientation="right"
                              label={{ 
                                value: t('metaDashboard.campaigns.legendSales'), 
                                angle: 90, 
                                position: 'insideRight',
                                style: { textAnchor: 'middle' }
                              }}
                              tickFormatter={(value) => formatCurrency(value)}
                              domain={[0, 'dataMax']}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend />
                            
                            {/* Show sales as bars */}
                            <Bar
                              yAxisId="right"
                              dataKey="sales_value"
                              name={t('metaDashboard.campaigns.legendSales')}
                              barSize={20}
                              fill="#00A3FF"
                              opacity={0.8}
                            />
                            
                            {/* Show conversion rate as a line */}
                            <Line
                              yAxisId="left"
                              type="monotone"
                              dataKey="conversion_rate"
                              stroke="#ff7300"
                              strokeWidth={3}
                              name={t('metaDashboard.campaigns.legendConversionRate')}
                              dot={{ fill: '#ff7300', strokeWidth: 2, r: 4 }}
                              connectNulls={false}
                            />
                            
                            {/* Add reference line for campaign period if available */}
                            {(() => {
                              const selectedCamp = campaigns.find(c => c.id === selectedCampaign);
                              if (!selectedCamp) return null;
                              
                              const elements = [];
                              
                              // Campaign start reference line
                              if (selectedCamp.start_time) {
                                try {
                                  const startDate = new Date(selectedCamp.start_time);
                                  if (!isNaN(startDate.getTime())) {
                                    elements.push(
                                      <ReferenceLine
                                        key="campaign-start"
                                        x={startDate.toISOString().split('T')[0]}
                                        stroke="#4CAF50"
                                        strokeDasharray="5 5"
                                        label="Campaign Start"
                                        ifOverflow="extendDomain"
                                        yAxisId="left"
                                      />
                                    );
                                  }
                                } catch (_error) {
                                  logger.warn('Invalid start_time for campaign:', selectedCamp.start_time);
                                }
                              }
                              
                              // Campaign end reference line
                              if (selectedCamp.end_time) {
                                try {
                                  const endDate = new Date(selectedCamp.end_time);
                                  if (!isNaN(endDate.getTime())) {
                                    elements.push(
                                      <ReferenceLine
                                        key="campaign-end"
                                        x={endDate.toISOString().split('T')[0]}
                                        stroke="#F44336"
                                        strokeDasharray="5 5"
                                        label="Campaign End"
                                        ifOverflow="extendDomain"
                                        yAxisId="left"
                                      />
                                    );
                                  }
                                } catch (_error) {
                                  logger.warn('Invalid end_time for campaign:', selectedCamp.end_time);
                                }
                              }
                              
                              return elements;
                            })()}
                          </ComposedChart>
                        </ChartContainer>
                          );
                        })()}
                      </Box>
                    </Paper>

                    {/* Campaign Comparison Table */}
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        {t('metaDashboard.campaigns.tableTitleComparisonData')}
                      </Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>{t('metaDashboard.campaigns.tableHeaderCampaign')}</TableCell>
                              <TableCell align="right">{t('metaDashboard.campaigns.tableHeaderConversionRate')}</TableCell>
                              <TableCell align="right">{t('metaDashboard.campaigns.tableHeaderLaNubeSales')}</TableCell>
                              <TableCell align="right">{t('metaDashboard.campaigns.tableHeaderImpact')}</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {campaigns.length > 0 ? campaigns.map((campaign) => {
                              const campaignCorrelation = campaign.id === selectedCampaign 
                                ? correlation 
                                : (campaign.correlation_metric || Math.random() * 0.8 - 0.3);
                              const attributedSalesValue = campaignAttributedSales[campaign.id] || 0;
                              
                              // Use real business impact calculation instead of just correlation
                              const campaignImpact = calculateRealBusinessImpact(
                                attributedSalesValue,
                                campaign.conversion_rate || 0,
                                campaign.spend || 0,
                                campaignCorrelation
                              );
                              
                              return (
                                <TableRow key={campaign.id} hover selected={campaign.id === selectedCampaign}>
                                  <TableCell component="th" scope="row">
                                    {getCampaignDisplayName(campaign)}
                                  </TableCell>
                                  <TableCell align="right">
                                    {formatPercentage(campaign.conversion_rate || 0)}
                                  </TableCell>
                                  <TableCell align="right">
                                    {formatCurrency(attributedSalesValue)}
                                  </TableCell>
                                  <TableCell align="right">
                                    {campaignImpact.toFixed(1)}%
                                  </TableCell>
                                </TableRow>
                              );
                            }) : (
                              <TableRow>
                                <TableCell colSpan={4} align="center">
                                  {t('metaDashboard.campaigns.noCampaignData')}
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </Paper>
                  </>
                )}
              </>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};