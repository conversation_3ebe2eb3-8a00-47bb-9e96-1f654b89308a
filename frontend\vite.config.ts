import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from 'tailwindcss';
import autoprefixer from 'autoprefixer';
import fs from 'fs';
import path from 'path';

export default defineConfig(({ mode }) => {
  // Only use HTTPS in development mode and only if cert files exist
  const useHttps = mode === 'development' &&
    fs.existsSync(path.resolve(__dirname, 'certs/cert.key')) &&
    fs.existsSync(path.resolve(__dirname, 'certs/cert.crt'));

  return {
    plugins: [react()],
    server: {
      port: 5173,
      ...(useHttps && {
        https: {
          key: fs.readFileSync(path.resolve(__dirname, 'certs/cert.key')),
          cert: fs.readFileSync(path.resolve(__dirname, 'certs/cert.crt')),
        },
      }),
      proxy: {
        '/api': {
          target: useHttps ? 'https://127.0.0.1:8000' : 'http://127.0.0.1:8000',
          changeOrigin: true,
          secure: false, // Allow self-signed certificates in development
          rewrite: (path) => path,
        },
      },
    },
    css: {
      postcss: {
        plugins: [
          tailwindcss(),
          autoprefixer(),
        ],
      },
    },
    resolve: {
      alias: {
        '@services': path.resolve(__dirname, 'src/services'),
        '@config': path.resolve(__dirname, 'config'),
      }
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : [],
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './src/tests/setup.ts',
      coverage: {
        reporter: ['text', 'html'],
      },
      // Ensure TypeScript configuration is properly applied for tests
      typecheck: {
        tsconfig: './tsconfig.app.json'
      }
    }
  };
});