/**
 * Instagram Business Service - Enhanced Instagram Business Account Integration
 * 
 * This service provides proper Instagram Business Account detection, authentication,
 * and data fetching with comprehensive error handling.
 */

import axios from '../config/axios';
import { logger } from '../utils/logger';
import { MetaPage, MetaError, MetaErrorType } from './types';
import { AxiosError } from 'axios';

export interface InstagramBusinessAccount {
  id: string;
  name: string;
  username?: string;
  access_token: string;
  platform: 'instagram';
  followers_count: number;
  follows_count?: number;
  media_count: number;
  profile_picture_url?: string;
  biography?: string;
  website?: string;
  account_type: 'business';
  parent_page_id: string;
  parent_page_name?: string;
  last_updated?: string;
}

export interface InstagramInsights {
  insights: {
    impressions: number;
    reach: number;
    profile_views: number;
    engagement: number;
    website_clicks?: number;
    saved?: number;
    media_count?: number;
    [key: string]: number | undefined;
  };
  account_id: string;
  store_id: string;
  time_range: string;
  start_date: string;
  end_date: string;
  last_updated: string;
}

export interface InstagramProfile {
  id: string;
  username?: string;
  name?: string;
  biography: string;
  followers_count: number;
  follows_count: number;
  media_count: number;
  profile_picture_url?: string;
  website?: string;
  platform: 'instagram';
  account_type: 'business';
  parent_page_id: string;
  parent_page_name?: string;
  last_updated?: string;
}

export interface InstagramConnectionResult {
  success: boolean;
  instagram_accounts: number;
  accounts: InstagramBusinessAccount[];
  store_id: string;
  timestamp: string;
}

export interface InstagramValidationResult {
  valid: boolean;
  error?: string;
  account_id: string;
  store_id: string;
  account_exists: boolean;
  timestamp: string;
}

export interface InstagramTokenRefreshResult {
  total_accounts: number;
  refreshed: number;
  failed: number;
  errors: string[];
  store_id: string;
  timestamp: string;
}

export interface InstagramCleanupResult {
  total_checked: number;
  removed: number;
  invalid_accounts: string[];
  store_id: string;
  timestamp: string;
}

export class InstagramBusinessService {
  // Simple cache to prevent concurrent calls
  private static ongoingRequests = new Map<string, Promise<InstagramBusinessAccount[]>>();
  
  /**
   * Connect Instagram Business Accounts from Facebook Pages
   * 
   * This method properly detects Instagram Business Accounts connected to
   * Facebook Pages and stores them with correct access tokens.
   */
  static async connectInstagramBusinessAccounts(
    facebookPages: MetaPage[],
    userAccessToken: string
  ): Promise<InstagramConnectionResult> {
    try {
      logger.info('Connecting Instagram Business Accounts', { 
        pageCount: facebookPages.length 
      });
  
      const response = await axios.post('/api/meta/connect-instagram', {
        facebook_pages: facebookPages,
        user_access_token: userAccessToken
      });
  
      logger.info('Successfully connected Instagram Business Accounts', {
        accountCount: response.data.instagram_accounts
      });
  
      return response.data;
  
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Failed to connect Instagram Business Accounts', axiosError);
      
      throw {
        type: MetaErrorType.API_ERROR,
        message: 'Failed to connect Instagram Business Accounts',
        originalError: axiosError
      } as MetaError;
    }
  }

  /**
   * Get all Instagram Business Accounts for the current store
   */
  static async getInstagramAccounts(): Promise<InstagramBusinessAccount[]> {
    const cacheKey = 'getInstagramAccounts';
    
    // Check if there's already an ongoing request
    if (this.ongoingRequests.has(cacheKey)) {
      logger.debug('Deduplicating concurrent Instagram accounts request');
      return this.ongoingRequests.get(cacheKey)!;
    }
    
    // Create the request promise
    const requestPromise = this.fetchInstagramAccountsInternal();
    
    // Store it in the cache
    this.ongoingRequests.set(cacheKey, requestPromise);
    
    try {
      const result = await requestPromise;
      return result;
    } finally {
      // Clean up the cache after request completes
      this.ongoingRequests.delete(cacheKey);
    }
  }
  
  /**
   * Internal method to fetch Instagram accounts
   */
  private static async fetchInstagramAccountsInternal(): Promise<InstagramBusinessAccount[]> {
    try {
      logger.debug('Fetching Instagram Business Accounts');
  
      const response = await axios.get('/api/meta/instagram-accounts');
      
      const accounts = response.data.accounts || [];
      logger.debug('Successfully fetched Instagram accounts', { 
        count: accounts.length 
      });
  
      return accounts;
  
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Failed to fetch Instagram accounts', axiosError);
      
      // Instead of throwing, return empty array to prevent loops
      if (axiosError.response?.status === 404) {
        logger.debug('No Instagram Business Accounts found for this store');
        return [];
      }
      
      if (axiosError.response?.status === 500) {
        logger.warn('Instagram accounts endpoint returned 500 - likely no accounts configured yet');
        return [];
      }
      
      // For network errors or other issues, also return empty array
      logger.warn('Instagram accounts fetch failed, returning empty array to prevent loops');
      return [];
    }
  }

  /**
   * Get Instagram insights with proper error handling
   */
  static async getInstagramInsights(
    accountId: string,
    storeId: string,
    timeRange: string = '30d',
    metrics?: string[],
    useCache: boolean = true
  ): Promise<InstagramInsights> {
    try {
      logger.debug('Fetching Instagram insights', { 
        accountId, 
        storeId,
        timeRange, 
        metrics,
        useCache 
      });
  
      const params = new URLSearchParams({
        time_range: timeRange,
        use_cache: useCache.toString()
      });
  
      if (metrics && metrics.length > 0) {
        params.append('metrics', metrics.join(','));
      }
  
      // If a specific page_id is provided and it's not 'auto', add it as a parameter
      if (accountId && accountId !== 'auto') {
        params.append('page_id', accountId);
      }
  
      const response = await axios.get(
        `/api/instagram/${storeId}/insights?${params.toString()}`
      );
  
      logger.debug('Successfully fetched Instagram insights', {
        accountId,
        storeId,
        timeRange: response.data.time_range,
        metrics: {
          impressions: response.data.impressions,
          reach: response.data.reach,
          engagement: response.data.engagement,
          profile_views: response.data.profile_views
        }
      });
  
      // Transform the response to match the expected InstagramInsights interface
      const transformedResponse: InstagramInsights = {
        insights: {
          impressions: response.data.impressions || 0,
          reach: response.data.reach || 0,
          profile_views: response.data.profile_views || 0,
          engagement: response.data.engagement || 0,
          website_clicks: response.data.website_clicks || 0,
          saved: response.data.saved || 0,
          media_count: response.data.media_count || 0
        },
        account_id: accountId,
        store_id: storeId,
        time_range: timeRange,
        start_date: response.data.date_range ? response.data.date_range.split(' to ')[0] : '',
        end_date: response.data.date_range ? response.data.date_range.split(' to ')[1] : '',
        last_updated: response.data.last_updated || new Date().toISOString()
      };
  
      return transformedResponse;
  
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Failed to fetch Instagram insights', axiosError);
      
      // Handle specific error codes
      if (axiosError.response?.status === 404) {
        throw {
          type: MetaErrorType.API_ERROR,
          message: 'Instagram Business Account not found',
          originalError: axiosError
        } as MetaError;
      }

      if (axiosError.response?.status === 401) {
        // Try to refresh the token first
        try {
          logger.info('Instagram token expired, attempting to refresh...');
          await InstagramBusinessService.refreshMetaToken();
          
          // Retry the request once with the refreshed token
          logger.info('Retrying Instagram insights request with refreshed token...');
          const retryParams = new URLSearchParams({
            time_range: timeRange,
            use_cache: useCache.toString()
          });
          
          if (metrics && metrics.length > 0) {
            retryParams.append('metrics', metrics.join(','));
          }
          
          if (accountId && accountId !== 'auto') {
            retryParams.append('page_id', accountId);
          }
          
          const retryResponse = await axios.get(
            `/api/instagram/${storeId}/insights?${retryParams.toString()}`
          );
          
          logger.info('Instagram insights retry successful after token refresh');
          
          // Transform the retry response to match the expected InstagramInsights interface
          const retryTransformedResponse: InstagramInsights = {
            insights: {
              impressions: retryResponse.data.impressions || 0,
              reach: retryResponse.data.reach || 0,
              profile_views: retryResponse.data.profile_views || 0,
              engagement: retryResponse.data.engagement || 0,
              website_clicks: retryResponse.data.website_clicks || 0,
              saved: retryResponse.data.saved || 0,
              media_count: retryResponse.data.media_count || 0
            },
            account_id: accountId,
            store_id: storeId,
            time_range: timeRange,
            start_date: retryResponse.data.date_range ? retryResponse.data.date_range.split(' to ')[0] : '',
            end_date: retryResponse.data.date_range ? retryResponse.data.date_range.split(' to ')[1] : '',
            last_updated: retryResponse.data.last_updated || new Date().toISOString()
          };
          
          return retryTransformedResponse;
          
        } catch (refreshError: unknown) {
          const axiosRefreshError = refreshError as AxiosError;
          logger.error('Failed to refresh token or retry request:', axiosRefreshError);
          throw {
            type: MetaErrorType.AUTH_FAILED,
            message: 'Instagram access token is invalid or expired. Please reconnect your Meta account.',
            originalError: axiosError
          } as MetaError;
        }
      }

      if (axiosError.response?.status === 403) {
        throw {
          type: MetaErrorType.PERMISSION_DENIED,
          message: 'Missing Instagram permissions',
          originalError: axiosError
        } as MetaError;
      }

      if (axiosError.response?.status === 429) {
        throw {
          type: MetaErrorType.RATE_LIMIT,
          message: 'Instagram API rate limit reached',
          originalError: axiosError
        } as MetaError;
      }

      throw {
        type: MetaErrorType.API_ERROR,
        message: 'Failed to fetch Instagram insights',
        originalError: axiosError
      } as MetaError;
    }
  }

  /**
   * Get Instagram profile information
   */
  static async getInstagramProfile(accountId: string, storeId: string): Promise<InstagramProfile> {
    try {
      logger.debug('Fetching Instagram profile', { accountId, storeId });

      const params = new URLSearchParams();
      if (accountId && accountId !== 'auto') {
        params.append('page_id', accountId);
      }

      const response = await axios.get(
        `/api/instagram/${storeId}/profile?${params.toString()}`
      );

      logger.debug('Successfully fetched Instagram profile', {
        accountId,
        storeId,
        username: response.data.data.username
      });

      return response.data.data;

    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Failed to fetch Instagram profile', axiosError);
      
      if (axiosError.response?.status === 404) {
        throw {
          type: MetaErrorType.API_ERROR,
          message: 'Instagram Business Account not found',
          originalError: axiosError
        } as MetaError;
      }

      throw {
        type: MetaErrorType.API_ERROR,
        message: 'Failed to fetch Instagram profile',
        originalError: axiosError
      } as MetaError;
    }
  }

  /**
   * Validate Instagram account access and permissions
   */
  static async validateInstagramAccount(accountId: string): Promise<InstagramValidationResult> {
    try {
      logger.debug('Validating Instagram account', { accountId });

      const response = await axios.get(
        `/api/meta/instagram/${accountId}/validate`
      );

      logger.debug('Instagram account validation result', {
        accountId,
        valid: response.data.valid
      });

      return response.data;

    } catch (error: unknown) {
      logger.error('Failed to validate Instagram account', error);
      
      // Handle specific error cases gracefully without throwing
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        logger.warn('Request was canceled, treating as validation failure');
        return {
          valid: false,
          error: 'Request was canceled',
          account_id: accountId,
          store_id: '',
          account_exists: false,
          timestamp: new Date().toISOString()
        };
      }

      if (error.response?.status === 401) {
        return {
          valid: false,
          error: 'Instagram access token is invalid or expired',
          account_id: accountId,
          store_id: '',
          account_exists: true,
          timestamp: new Date().toISOString()
        };
      }

      if (error.response?.status === 403) {
        return {
          valid: false,
          error: 'Authentication failed - please reconnect your Meta account',
          account_id: accountId,
          store_id: '',
          account_exists: false,
          timestamp: new Date().toISOString()
        };
      }

      if (error.response?.status === 404) {
        return {
          valid: false,
          error: 'Instagram Business Account not found for this store',
          account_id: accountId,
          store_id: '',
          account_exists: false,
          timestamp: new Date().toISOString()
        };
      }
      
      // For any other errors, return validation failure instead of throwing
      return {
        valid: false,
        error: `Validation failed: ${error.message || 'Unknown error'}`,
        account_id: accountId,
        store_id: '',
        account_exists: false,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Refresh access tokens for all Instagram accounts
   */
  static async refreshInstagramTokens(): Promise<InstagramTokenRefreshResult> {
    try {
      logger.info('Refreshing Instagram access tokens');

      const response = await axios.post('/api/meta/instagram/refresh-tokens');

      logger.info('Instagram token refresh completed', {
        refreshed: response.data.refreshed,
        failed: response.data.failed
      });

      return response.data;

    } catch (error: unknown) {
      logger.error('Failed to refresh Instagram tokens', error);
      
      throw {
        type: MetaErrorType.API_ERROR,
        message: 'Failed to refresh Instagram tokens',
        originalError: error
      } as MetaError;
    }
  }

  /**
   * Refresh Meta token using the backend endpoint
   */
  static async refreshMetaToken(): Promise<void> {
    try {
      logger.info('Attempting to refresh Meta token via backend...');
      
      const response = await axios.post('/api/auth/refresh-meta-token');
      
      if (response.data.success) {
        logger.info('Meta token refreshed successfully via backend');
      } else {
        throw new Error(response.data.message || 'Backend token refresh failed');
      }
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Backend token refresh failed:', axiosError);
      throw axiosError;
    }
  }

  /**
   * Handle expired Instagram token by triggering a fresh Meta login
   */
  static async handleExpiredInstagramToken(): Promise<{
    success: boolean;
    message: string;
  }> {
    try {
      logger.info('Handling expired Instagram token - triggering fresh Meta connection');
      
      // Import MetaAuthService dynamically to avoid circular dependencies
      const { MetaAuthService } = await import('./auth');
      
      // Clear existing Meta session
      await MetaAuthService.logout();
      
      // Trigger fresh login
      const loginResult = await MetaAuthService.login('facebook');
      
      if (loginResult && loginResult.authResponse) {
        // Handle the new auth with backend
        await MetaAuthService.handleMetaAuth(loginResult, 'facebook');
        
        logger.info('Successfully refreshed Meta connection for Instagram');
        return {
          success: true,
          message: 'Instagram connection refreshed successfully'
        };
      } else {
        throw new Error('Failed to get new Meta authorization');
      }
      
    } catch (error: unknown) {
      const axiosError = error as AxiosError;
      logger.error('Failed to refresh Instagram connection', axiosError);
      return {
        success: false,
        message: `Failed to refresh Instagram connection: ${axiosError.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Clean up invalid Instagram accounts
   */
  static async cleanupInstagramAccounts(): Promise<InstagramCleanupResult> {
    try {
      logger.info('Cleaning up invalid Instagram accounts');

      const response = await axios.post('/api/meta/instagram/cleanup');

      logger.info('Instagram account cleanup completed', {
        removed: response.data.removed,
        checked: response.data.total_checked
      });

      return response.data;

    } catch (error: AxiosError) {
      logger.error('Failed to cleanup Instagram accounts', axiosError);
      
      throw {
        type: MetaErrorType.API_ERROR,
        message: 'Failed to cleanup Instagram accounts',
        originalError: axiosError
      } as MetaError;
    }
  }

  /**
   * Enhanced Instagram Business Account detection and connection flow
   * 
   * This method provides a complete flow for detecting and connecting
   * Instagram Business Accounts with proper error handling.
   */
  static async detectAndConnectInstagramAccounts(
    facebookPages: MetaPage[],
    userAccessToken: string
  ): Promise<{
    success: boolean;
    accounts: InstagramBusinessAccount[];
    errors: string[];
    summary: string;
  }> {
    try {
      logger.info('Starting Instagram Business Account detection flow');

      // Filter Facebook pages that might have Instagram connections
      const pagesWithTokens = facebookPages.filter(page => page.access_token);
      
      if (pagesWithTokens.length === 0) {
        return {
          success: false,
          accounts: [],
          errors: ['No Facebook pages with access tokens found'],
          summary: 'Cannot detect Instagram accounts without valid Facebook page tokens'
        };
      }

      logger.debug('Checking Facebook pages for Instagram connections', {
        totalPages: facebookPages.length,
        pagesWithTokens: pagesWithTokens.length
      });

      // Connect Instagram accounts
      const connectionResult = await this.connectInstagramBusinessAccounts(
        pagesWithTokens,
        userAccessToken
      );

      // Validate connected accounts
      const validationPromises = connectionResult.accounts.map(account => 
        this.validateInstagramAccount(account.id).catch((error: unknown) => {
          logger.warn('Account validation failed', { accountId: account.id, error });
          return { valid: false, error: error.message };
        })
      );

      const validationResults = await Promise.all(validationPromises);
      const invalidAccounts = validationResults.filter(result => !result.valid);

      const errors: string[] = [];
      if (invalidAccounts.length > 0) {
        errors.push(`${invalidAccounts.length} accounts failed validation`);
      }

      const summary = connectionResult.instagram_accounts > 0
        ? `Successfully connected ${connectionResult.instagram_accounts} Instagram Business Account(s)`
        : 'No Instagram Business Accounts found connected to your Facebook Pages';

      logger.info('Instagram detection flow completed', {
        accountsFound: connectionResult.instagram_accounts,
        errors: errors.length
      });

      return {
        success: connectionResult.instagram_accounts > 0,
        accounts: connectionResult.accounts,
        errors,
        summary
      };

    } catch (error: unknown) {
      logger.error('Instagram detection flow failed', error);
      
      return {
        success: false,
        accounts: [],
        errors: [error.message || 'Unknown error occurred'],
        summary: 'Failed to detect Instagram Business Accounts'
      };
    }
  }

  /**
   * Check if Instagram Business Account is properly configured
   */
  static async checkInstagramConfiguration(): Promise<{
    hasAccounts: boolean;
    accountCount: number;
    needsSetup: boolean;
    issues: string[];
  }> {
    try {
      const accounts = await this.getInstagramAccounts();
      
      const issues: string[] = [];
      
      // Check each account
      for (const account of accounts) {
        try {
          const validation = await this.validateInstagramAccount(account.id);
          if (!validation.valid) {
            issues.push(`Account ${account.username || account.id}: ${validation.error}`);
          }
        } catch (_error: AxiosError) {
          issues.push(`Account ${account.username || account.id}: Validation failed`);
        }
      }

      return {
        hasAccounts: accounts.length > 0,
        accountCount: accounts.length,
        needsSetup: accounts.length === 0 || issues.length > 0,
        issues
      };

    } catch (_error: AxiosError) {
      logger.error('Failed to check Instagram configuration', _error);
      
      return {
        hasAccounts: false,
        accountCount: 0,
        needsSetup: true,
        issues: ['Failed to check Instagram configuration']
      };
    }
  }
}

export default InstagramBusinessService;