import { fetchWithDeduplication, ApiError } from './apiService';
import {
  MetaPost,
  MetaComment,
  PostMetrics,
  EngagementMetrics,
  FollowerData,
  FollowerDemographics,
  MetaAdAccount,
  MetaAdCampaign,
  MetaCampaignBasic,
  AdPerformance,
  MetaPage,
  MetaInsight,
  TimeRange,
  InstagramAdMetrics,
} from './types';
import { MetaDataService } from './dataService';
import logger from '../utils/logger'; // Import the logger
import { authService } from '../services/authService';
import { AuthUtils } from '../utils/authUtils';

// Generic paginated response interface
interface PaginatedResponse<T> {
  data: T[];
  paging: {
    cursors?: {
      before: string;
      after: string;
    };
    next?: string;
    previous?: string;
  };
}

// Demographics response interfaces
interface AgeGenderItem {
  age_range: string;
  gender: 'Male' | 'Female' | 'Other';
  count: number;
}

interface CountryItem {
  country_code: string;
  city?: string;
  count: number;
}

interface DemographicsResponse {
  demographics: {
    age_gender: AgeGenderItem[];
    countries: CountryItem[];
  };
}

// Ad metrics response interface
export interface AdMetricsResponse {
  overview: {
    total_spend: number;
    total_impressions: number;
    total_reach: number;
    total_clicks: number;
    total_conversions: number;
    average_cpc: number;
    average_ctr: number;
    average_cost_per_conversion: number;
    average_roi: number;
    roas: number;
    account_currency?: string;
  };
  daily_metrics: Array<{
    date: string;
    spend: number;
    impressions: number;
    clicks: number;
    conversions: number;
  }>;
  campaigns: MetaCampaignBasic[];
  // Added optional organic property to align with potential backend response
  organic?: {
    engagement?: {
      likes?: number;
      comments?: number;
      shares?: number;
      saves?: number;
    };
    profile_activity?: {
      profile_visits?: number;
      messages?: number;
      link_clicks?: number;
    };
  };
}

// Define interface for CampaignApiResponseItem that's used in ProcessedCampaign but not exported
interface CampaignApiResponseItem {
  id: string;
  name: string;
  status?: string;
  objective?: string;
  daily_budget?: string;
  lifetime_budget?: string;
  start_time?: string;
  end_time?: string;
  spend?: number;
  impressions?: number;
  clicks?: number;
  conversions?: number;
}

// Define the CampaignMetrics interface matching dataService.ts
interface CampaignMetrics {
  spend: number;
  views: number;
  clicks: number;
  conversions: number;
  ctr: number;
  roi: number;
  impressions?: number;
}

// Define the ProcessedCampaign interface matching dataService.ts
interface ProcessedCampaign extends CampaignApiResponseItem {
  metrics: CampaignMetrics;
  budget?: number;
}

// Define FacebookDailyAdMetric interface for daily_metrics
interface FacebookDailyAdMetric {
  date: string;
  spend: number;
  views: number;
  reach: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
}

// Import FacebookAdMetricsResponse interface explicitly to match the one in dataService.ts
interface FacebookAdMetricsResponse {
  overview: {
    total_spend: number;
    total_impressions: number;
    total_reach: number;
    total_clicks: number;
    total_conversions: number;
    average_cpc: number;
    average_ctr: number;
    average_cost_per_conversion: number;
    average_roi: number;
    roas: number;
    account_currency?: string;
  };
  daily_metrics: FacebookDailyAdMetric[];
  campaigns: ProcessedCampaign[];
  organic?: {
    engagement?: {
      likes?: number;
      comments?: number;
      shares?: number;
      saves?: number;
    };
    profile_activity?: {
      profile_visits?: number;
      messages?: number;
      link_clicks?: number;
    };
  };
}

interface MongoDBMetricsResponse {
  metrics?: FacebookAdMetricsResponse;
}

interface FacebookAdMetricsResponseWithOptionals extends FacebookAdMetricsResponse {
  account_currency?: string;
  organic?: {
    engagement?: {
      likes?: number;
      comments?: number;
      shares?: number;
      saves?: number;
    };
    profile_activity?: {
      profile_visits?: number;
      messages?: number;
      link_clicks?: number;
    };
  };
}

interface RawMetricData {
  date?: string;
  spend?: number;
  impressions?: number;
  views?: number;
  clicks?: number;
  conversions?: number;
}

interface RawCampaignData {
  id?: string | number;
  name?: string;
  status?: string;
  spend?: number;
  impressions?: number;
  clicks?: number;
  conversions?: number;
  metrics?: {
    spend?: number;
    impressions?: number;
    views?: number;
    clicks?: number;
    conversions?: number;
  };
}

// Define the new types near the top or before the class definition
type SyncStatusResponse = {
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  last_synced: string | null;
  message: string;
};

type MockDataResponse = Record<string, unknown>;

// Define the interface for a flat MongoDB metric document
interface MongoFlatMetric {
  campaign_id?: string;
  page_id: string;
  store_id: string;
  date?: string;
  impressions?: number;
  reach?: number;
  clicks?: number;
  conversions?: number;
  spend?: number;
  ctr?: number;
  cpc?: number;
  cost_per_conversion?: number;
  currency?: string;
  platform?: string;
  updated_at?: string;
}

// Type guard for array of flat MongoDB metric documents
function isFlatMongoDbMetricsArray(response: unknown): response is Array<MongoFlatMetric> {
  if (Array.isArray(response)) {
    logger.debug('Checking if response is an array of flat MongoDB metric documents:', response);
    // Check if at least one element has the expected keys
    return response.some(doc =>
      doc && typeof doc === 'object' &&
      'page_id' in doc &&
      'store_id' in doc &&
      ('impressions' in doc || 'spend' in doc || 'clicks' in doc)
    );
  }
  return false;
}

/**
 * Service for storing and retrieving Meta data from MongoDB
 */
export class MetaStoreService {
  /**
   * Save a connected Meta page
   * @param page Meta page to save
   * @returns ID of the saved page
   *
   * NOTE: This method is typed for future backend integration. The backend endpoint /api/meta/pages (POST) must exist for this to work.
   */
  static async savePage(page: MetaPage): Promise<string> {
    try {
      const data = await fetchWithDeduplication<{id: string}>(`/api/meta/pages`, {
        method: 'POST',
        headers: AuthUtils.getApiHeaders(),
        body: JSON.stringify(page)
      });
      return data.id;
    } catch (error) {
      logger.error('Error saving Meta page:', error);
      AuthUtils.handleApiError(error, 'Save Meta page');
    }
  }

  /**
   * Get connected Meta pages for the current user
   * @returns Array of Meta pages
   *
   * NOTE: This method is typed for future backend integration. The backend endpoint /api/meta/pages (GET) must exist for this to work.
   */
  static async getPages(): Promise<MetaPage[]> {
    try {
      const data = await fetchWithDeduplication<MetaPage[]>(`/api/meta/pages`, {
        headers: AuthUtils.getApiHeaders()
      });
      return data;
    } catch (error) {
      logger.error('Error fetching Meta pages:', error);
      AuthUtils.handleApiError(error, 'Fetch Meta pages');
    }
  }

  /**
   * Save posts to MongoDB
   * @param posts Array of Meta posts to save
   */
  static async savePosts(posts: MetaPost[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/posts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ posts })
      });
    } catch (error) {
      logger.error('Error saving posts:', error);
      throw new Error('Failed to save posts');
    }
  }

  /**
   * Get posts for a specific page
   * @param pageId ID of the page
   * @param options Query options (limit, offset, timeRange)
   * @returns Array of Meta posts
   */
  static async getPostsByPage(
    pageId: string,
    options: { limit?: number; offset?: number; timeRange?: TimeRange } = {}
  ): Promise<MetaPost[]> {
    try {
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit.toString());
      if (options.offset) queryParams.append('offset', options.offset.toString());
      if (options.timeRange) {
        if (options.timeRange.since) queryParams.append('since', options.timeRange.since);
        if (options.timeRange.until) queryParams.append('until', options.timeRange.until);
      }

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await fetchWithDeduplication<PaginatedResponse<MetaPost>>(
        `/api/meta/pages/${pageId}/posts${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      
      return response.data || [];
    } catch (error) {
      logger.error('Error fetching posts:', error);
      
      // Handle API errors
      if (error instanceof ApiError) {
        throw new Error(error.message);
      }
      
      throw error instanceof Error ? error : new Error('Failed to fetch posts. Please try again later.');
    }
  }

  /**
   * Save post metrics to MongoDB
   * @param metrics Array of post metrics to save
   */
  static async savePostMetrics(metrics: PostMetrics[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/post-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ metrics })
      });
    } catch (error) {
      logger.error('Error saving post metrics:', error);
      throw new Error('Failed to save post metrics');
    }
  }

  /**
   * Get metrics for a specific post
   * @param postId ID of the post
   * @returns Post metrics
   */
  static async getPostMetrics(postId: string): Promise<PostMetrics> {
    try {
      const response = await fetchWithDeduplication<PostMetrics>(`/api/meta/posts/${postId}/metrics`, {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });
      
      return response;
    } catch (error) {
      logger.error('Error fetching post metrics:', error);
      
      // Handle API errors
      if (error instanceof ApiError) {
        throw new Error(error.message);
      }
      
      throw error instanceof Error ? error : new Error('Failed to fetch post metrics. Please try again later.');
    }
  }

  /**
   * Save comments to MongoDB
   * @param comments Array of comments to save
   */
  static async saveComments(comments: MetaComment[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ comments })
      });
    } catch (error) {
      logger.error('Error saving comments:', error);
      throw new Error('Failed to save comments');
    }
  }

  /**
   * Get comments for a specific post
   * @param postId ID of the post
   * @param options Query options (limit, offset)
   * @returns Array of comments
   */
  static async getCommentsByPost(
    postId: string,
    options: { limit?: number; offset?: number } = {}
  ): Promise<MetaComment[]> {
    try {
      const queryParams = new URLSearchParams();
      if (options.limit) queryParams.append('limit', options.limit.toString());
      if (options.offset) queryParams.append('offset', options.offset.toString());

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const response = await fetchWithDeduplication<PaginatedResponse<MetaComment>>(
        `/api/meta/posts/${postId}/comments${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      
      return response.data || [];
    } catch (error) {
      logger.error('Error fetching comments:', error);
      
      // Handle API errors
      if (error instanceof ApiError) {
        throw new Error(error.message);
      }
      
      throw error instanceof Error ? error : new Error('Failed to fetch comments. Please try again later.');
    }
  }

  /**
   * Save engagement metrics to MongoDB
   * @param pageId ID of the page
   * @param metrics Engagement metrics to save
   */
  static async saveEngagementMetrics(pageId: string, metrics: EngagementMetrics): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/pages/${pageId}/engagement-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify(metrics)
      });
    } catch (error) {
      logger.error('Error saving engagement metrics:', error);
      throw new Error('Failed to save engagement metrics');
    }
  }

  /**
   * Get engagement metrics for a specific page
   * @param pageId ID of the page
   * @param timeRange Optional time range for filtering metrics
   * @returns Engagement metrics
   */
  static async getEngagementMetrics(pageId: string, timeRange?: TimeRange): Promise<EngagementMetrics> {
    const queryParams = new URLSearchParams();
    if (timeRange) {
      queryParams.append('since', timeRange.since);
      queryParams.append('until', timeRange.until);
    }

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    try {
      return await fetchWithDeduplication<EngagementMetrics>(
        `/api/meta/pages/${pageId}/engagement-metrics${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
    } catch (error) {
      logger.error('Error fetching engagement metrics:', error);
      throw new Error('Failed to fetch engagement metrics');
    }
  }

  /**
   * Save follower data to MongoDB
   * @param pageId ID of the page
   * @param data Follower data to save
   */
  static async saveFollowerData(pageId: string, data: FollowerData): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/pages/${pageId}/follower-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify(data)
      });
    } catch (error) {
      logger.error('Error saving follower data:', error);
      throw new Error('Failed to save follower data');
    }
  }

  /**
   * Get follower data for a specific page
   * @param pageId ID of the page
   * @param timeRange Optional time range for filtering data
   * @returns Follower data
   */
  static async getFollowerData(pageId: string, timeRange?: TimeRange): Promise<FollowerData> {
    const queryParams = new URLSearchParams();
    if (timeRange) {
      queryParams.append('since', timeRange.since);
      queryParams.append('until', timeRange.until);
    }

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    try {
      return await fetchWithDeduplication<FollowerData>(
        `/api/meta/pages/${pageId}/follower-data${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
    } catch (error) {
      logger.error('Error fetching follower data:', error);
      throw new Error('Failed to fetch follower data');
    }
  }

  /**
   * Save follower demographics to MongoDB
   * @param pageId ID of the page
   * @param demographics Follower demographics to save
   */
  static async saveFollowerDemographics(pageId: string, demographics: FollowerDemographics): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/pages/${pageId}/follower-demographics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify(demographics)
      });
    } catch (error) {
      logger.error('Error saving follower demographics:', error);
      throw new Error('Failed to save follower demographics');
    }
  }

  /**
   * Get follower demographics for a specific page
   * @param pageId ID of the page
   * @returns Follower demographics
   */
  static async getFollowerDemographics(pageId: string): Promise<FollowerDemographics> {
    try {
      const response = await fetchWithDeduplication<DemographicsResponse>(
        `/api/meta/pages/${pageId}/follower-demographics`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      
      // Map API response to the expected FollowerDemographics format
      const demographics: FollowerDemographics = {
        page_id: pageId,
        age_ranges: [],
        gender: [],
        top_locations: []
      };
      
      // Map age_gender data to age_ranges and gender
      if (response.demographics && response.demographics.age_gender) {
        // Process age ranges
        const ageGenderMap = new Map<string, {male: number, female: number, other: number}>();
        
        // Group by age range
        response.demographics.age_gender.forEach((item: AgeGenderItem) => {
          const existing = ageGenderMap.get(item.age_range) || {male: 0, female: 0, other: 0};
          
          if (item.gender === 'Male') {
            existing.male += item.count;
          } else if (item.gender === 'Female') {
            existing.female += item.count;
          } else {
            existing.other += item.count;
          }
          
          ageGenderMap.set(item.age_range, existing);
        });
        
        // Calculate total count for percentages
        const totalCount = Array.from(ageGenderMap.values()).reduce(
          (sum, counts) => sum + counts.male + counts.female + counts.other, 
          0
        );
        
        // Convert to age_ranges format
        demographics.age_ranges = Array.from(ageGenderMap.entries()).map(([range, counts]) => {
          const total = counts.male + counts.female + counts.other;
          return {
            range,
            percentage: totalCount > 0 ? total / totalCount : 0
          };
        });
        
        // Convert to gender format
        const maleTotal = Array.from(ageGenderMap.values()).reduce((sum, counts) => sum + counts.male, 0);
        const femaleTotal = Array.from(ageGenderMap.values()).reduce((sum, counts) => sum + counts.female, 0);
        const otherTotal = Array.from(ageGenderMap.values()).reduce((sum, counts) => sum + counts.other, 0);
        
        if (maleTotal > 0) {
          demographics.gender.push({
            type: 'Male',
            percentage: totalCount > 0 ? maleTotal / totalCount : 0
          });
        }
        
        if (femaleTotal > 0) {
          demographics.gender.push({
            type: 'Female',
            percentage: totalCount > 0 ? femaleTotal / totalCount : 0
          });
        }
        
        if (otherTotal > 0) {
          demographics.gender.push({
            type: 'Other',
            percentage: totalCount > 0 ? otherTotal / totalCount : 0
          });
        }
      }
      
      // Map countries/locations data to top_locations
      if (response.demographics && response.demographics.countries) {
        const totalCount = response.demographics.countries.reduce(
          (sum: number, item: CountryItem) => sum + item.count, 
          0
        );
        
        demographics.top_locations = response.demographics.countries.map((item: CountryItem) => ({
          country: item.country_code,
          city: item.city || '',
          percentage: totalCount > 0 ? item.count / totalCount : 0
        }));
      }
      
      return demographics;
    } catch (error) {
      logger.error('Error fetching follower demographics:', error);
      
      // Handle API errors
      if (error instanceof ApiError) {
        throw new Error(error.message);
      }
      
      throw error instanceof Error ? error : new Error('Failed to fetch demographics data. Please try again later.');
    }
  }

  /**
   * Save ad accounts to MongoDB
   * @param businessId ID of the business
   * @param accounts Array of ad accounts to save
   */
  static async saveAdAccounts(businessId: string, accounts: MetaAdAccount[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/business/${businessId}/ad-accounts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ accounts })
      });
    } catch (error) {
      logger.error('Error saving ad accounts:', error);
      throw new Error('Failed to save ad accounts');
    }
  }

  /**
   * Get ad accounts for a specific business
   * @param businessId ID of the business
   * @returns Array of ad accounts
   */
  static async getAdAccounts(businessId: string): Promise<MetaAdAccount[]> {
    try {
      const data = await fetchWithDeduplication<MetaAdAccount[]>(`/api/meta/business/${businessId}/ad-accounts`, {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });
      return data;
    } catch (error) {
      logger.error('Error fetching ad accounts:', error);
      throw new Error('Failed to fetch ad accounts');
    }
  }

  /**
   * Save ad campaigns to MongoDB
   * @param adAccountId ID of the ad account
   * @param campaigns Array of ad campaigns to save
   */
  static async saveAdCampaigns(adAccountId: string, campaigns: MetaAdCampaign[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/ad-accounts/${adAccountId}/campaigns`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ campaigns })
      });
    } catch (error) {
      logger.error('Error saving ad campaigns:', error);
      throw new Error('Failed to save ad campaigns');
    }
  }

  /**
   * Get ad campaigns for a specific ad account
   * @param adAccountId ID of the ad account
   * @returns Array of ad campaigns
   */
  static async getAdCampaigns(adAccountId: string): Promise<MetaAdCampaign[]> {
    try {
      const data = await fetchWithDeduplication<MetaAdCampaign[]>(`/api/meta/ad-accounts/${adAccountId}/campaigns`, {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });
      return data;
    } catch (error) {
      logger.error('Error fetching ad campaigns:', error);
      throw new Error('Failed to fetch ad campaigns');
    }
  }

  /**
   * Save ad performance metrics to MongoDB
   * @param _campaignId ID of the campaign (unused, required for interface compliance)
   * @param _performance Array of ad performance metrics to save (unused, required for interface compliance)
   *
   * Variables are intentionally unused to satisfy interface requirements and for future extensibility.
   */
  static async saveAdPerformance(_campaignId: string, _performance: AdPerformance[]): Promise<void> {
    // Reference parameters to satisfy linter and document intent
    void _campaignId;
    void _performance;
    // Defensive: throw if called for Facebook (should be guarded in dataService)
    throw new Error('saveAdPerformance is not implemented for Facebook campaigns. This should be guarded in the caller.');
  }

  /**
   * Save Instagram ad metrics to MongoDB
   * @param pageId ID of the Instagram page
   * @param adMetrics Instagram ad metrics data to save
   */
  static async saveInstagramAdMetrics(pageId: string, adMetrics: InstagramAdMetrics): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/instagram-ad-metrics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ 
          page_id: pageId,
          metrics: adMetrics 
        })
      });
      
      logger.info(`Successfully saved Instagram ad metrics for page ${pageId}`);
    } catch (error) {
      logger.error('Error saving Instagram ad metrics:', error);
      throw new Error('Failed to save Instagram ad metrics');
    }
  }

  /**
   * Get ad performance metrics for a specific campaign
   * @param campaignId ID of the campaign
   * @param timeRange Optional time range for filtering metrics
   * @returns Array of ad performance metrics
   */
  static async getAdPerformance(campaignId: string, timeRange?: TimeRange): Promise<AdPerformance[]> {
    const queryParams = new URLSearchParams();
    if (timeRange) {
      queryParams.append('since', timeRange.since);
      queryParams.append('until', timeRange.until);
    }

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    try {
      const data = await fetchWithDeduplication<AdPerformance[]>(
        `/api/meta/campaigns/${campaignId}/performance${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      return data;
    } catch (error) {
      logger.error('Error fetching ad performance:', error);
      throw new Error('Failed to fetch ad performance');
    }
  }

  /**
   * Save insights to MongoDB
   * @param insights Array of insights to save
   */
  static async saveInsights(insights: MetaInsight[]): Promise<void> {
    try {
      await fetchWithDeduplication(`/api/meta/insights`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        },
        body: JSON.stringify({ insights })
      });
    } catch (error) {
      logger.error('Error saving insights:', error);
      throw new Error('Failed to save insights');
    }
  }

  /**
   * Get insights by type
   * @param type Type of insights to retrieve
   * @param limit Maximum number of insights to retrieve
   * @returns Array of insights
   */
  static async getInsightsByType(
    type: string,
    limit: number = 10
  ): Promise<MetaInsight[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('type', type);
    queryParams.append('limit', limit.toString());

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    try {
      const data = await fetchWithDeduplication<MetaInsight[]>(
        `/api/meta/insights${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      return data;
    } catch (error) {
      logger.error('Error fetching insights:', error);
      throw new Error('Failed to fetch insights');
    }
  }

  /**
   * Get recent insights for all types
   * @param limit Maximum number of insights to retrieve
   * @returns Array of insights
   */
  static async getRecentInsights(limit: number = 10): Promise<MetaInsight[]> {
    const queryParams = new URLSearchParams();
    queryParams.append('limit', limit.toString());

    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    try {
      const data = await fetchWithDeduplication<MetaInsight[]>(
        `/api/meta/insights/recent${queryString}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      return data;
    } catch (error) {
      logger.error('Error fetching recent insights:', error);
      throw new Error('Failed to fetch recent insights');
    }
  }

  /**
   * Synchronize Meta data for a specific page
   * @param pageId ID of the page to synchronize
   * @returns Synchronization status
   */
  static async synchronizePageData(pageId: string): Promise<{ status: string; message: string }> {
    try {
      return await fetchWithDeduplication<{ status: string; message: string }>(`/api/meta/pages/${pageId}/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });
    } catch (error) {
      logger.error('Error synchronizing page data:', error);
      throw new Error('Failed to synchronize page data');
    }
  }

  /**
   * Get synchronization status for a page
   * @param pageId Meta page ID
   * @returns Synchronization status
   */
  static async getSyncStatus(pageId: string): Promise<SyncStatusResponse> {
    try {
      const data = await fetchWithDeduplication<SyncStatusResponse>(`/api/meta/pages/${pageId}/sync-status`, {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });
      return data;
    } catch (error) {
      logger.error('Error fetching sync status:', error);
      throw new Error('Failed to fetch sync status');
    }
  }

  /**
   * Get mock data when not connected to Meta
   */
  static async getMockData(): Promise<MockDataResponse> {
    try {
      const data = await fetchWithDeduplication<MockDataResponse>(`/api/meta/mock-data`, {
        headers: {
          'Authorization': `Bearer ${authService.getToken()}`
        }
      });

      return data;
    } catch (error) {
      logger.error('Error fetching mock data:', error);
      throw error;
    }
  }

  /**
   * Convert Facebook Ad Metrics to the standardized AdMetricsResponse format,
   * handling both API responses and MongoDB stored data,
   * and ensuring compatibility between the types.
   */
  private static convertFacebookAdMetricsToAdMetricsResponse(
    response: MongoDBMetricsResponse | FacebookAdMetricsResponseWithOptionals | AdMetricsResponse
  ): AdMetricsResponse {
    logger.debug('Converting metrics data to AdMetricsResponse format', response);

    // Check if the response is already in AdMetricsResponse format
    if (MetaStoreService.isAdMetricsResponse(response) && !('metrics' in response)) {
      logger.debug('Response is already AdMetricsResponse, returning directly.');
      return response as AdMetricsResponse; // Type assertion
    }

    // Check if this is MongoDB format (nested in metrics field) or direct FacebookAdMetricsResponseWithOptionals
    const isMongoDBFormat = 'metrics' in response && response.metrics !== undefined;
    const sourceData: FacebookAdMetricsResponseWithOptionals | AdMetricsResponse['overview'] | AdMetricsResponse = isMongoDBFormat
      ? response.metrics as FacebookAdMetricsResponseWithOptionals
      : response as (FacebookAdMetricsResponseWithOptionals | AdMetricsResponse);

    // Safely extract values with type checking
    // If sourceData is already an AdMetricsResponse overview, use it directly
    const overview = (sourceData as AdMetricsResponse)?.overview || (sourceData as FacebookAdMetricsResponseWithOptionals)?.overview || {};
    const dailyMetrics = Array.isArray((sourceData as FacebookAdMetricsResponseWithOptionals)?.daily_metrics) ? (sourceData as FacebookAdMetricsResponseWithOptionals).daily_metrics : [];
    const campaigns = Array.isArray((sourceData as FacebookAdMetricsResponseWithOptionals)?.campaigns) ? (sourceData as FacebookAdMetricsResponseWithOptionals).campaigns : [];
    const organicData = (sourceData as FacebookAdMetricsResponseWithOptionals)?.organic;

    // Debug logging for transformation
    logger.debug(`Processing ${dailyMetrics.length} daily metrics and ${campaigns.length} campaigns`);
    
    // Map daily metrics with thorough validation
    const mappedDailyMetrics = dailyMetrics.map((metric: RawMetricData) => ({
      date: typeof metric.date === 'string' ? metric.date : new Date().toISOString().split('T')[0],
      spend: typeof metric.spend === 'number' ? metric.spend : 0,
      impressions: typeof metric.impressions === 'number' ? metric.impressions : 
                 (typeof metric.views === 'number' ? metric.views : 0),
      clicks: typeof metric.clicks === 'number' ? metric.clicks : 0,
      conversions: typeof metric.conversions === 'number' ? metric.conversions : 0
    }));
    
    // Map campaigns to MetaCampaignBasic with thorough validation
    const mappedCampaigns: MetaCampaignBasic[] = campaigns.map((campaign: RawCampaignData) => {
      // Extract metrics with safe fallbacks
      const metrics = campaign.metrics || {};
      const spend = typeof campaign.spend === 'number' ? campaign.spend : 
                   (typeof metrics.spend === 'number' ? metrics.spend : 0);
      
      const impressions = typeof campaign.impressions === 'number' ? campaign.impressions : 
                         (typeof metrics.impressions === 'number' ? metrics.impressions : 
                         (typeof metrics.views === 'number' ? metrics.views : 0));
      
      const clicks = typeof campaign.clicks === 'number' ? campaign.clicks : 
                    (typeof metrics.clicks === 'number' ? metrics.clicks : 0);
      
      const conversions = typeof campaign.conversions === 'number' ? campaign.conversions : 
                         (typeof metrics.conversions === 'number' ? metrics.conversions : 0);
      
      return {
        id: typeof campaign.id === 'string' ? campaign.id : String(campaign.id || ''),
        name: typeof campaign.name === 'string' ? campaign.name : String(campaign.name || ''),
        status: typeof campaign.status === 'string' ? campaign.status : 'UNKNOWN',
        spend,
        impressions,
        clicks,
        conversions
      };
    });
    
    // Create overview with thorough validation
    const validatedOverview: AdMetricsResponse['overview'] = {
      total_spend: typeof overview.total_spend === 'number' ? overview.total_spend : 0,
      total_impressions: typeof overview.total_impressions === 'number' ? overview.total_impressions : 0,
      total_reach: typeof overview.total_reach === 'number' ? overview.total_reach : 0,
      total_clicks: typeof overview.total_clicks === 'number' ? overview.total_clicks : 0,
      total_conversions: typeof overview.total_conversions === 'number' ? overview.total_conversions : 0,
      average_cpc: typeof overview.average_cpc === 'number' ? overview.average_cpc : 0,
      average_ctr: typeof overview.average_ctr === 'number' ? overview.average_ctr : 0,
      average_cost_per_conversion: typeof overview.average_cost_per_conversion === 'number' ? overview.average_cost_per_conversion : 0,
      average_roi: typeof overview.average_roi === 'number' ? overview.average_roi : 0,
      roas: typeof overview.roas === 'number' ? overview.roas : 0,
      account_currency: typeof overview.account_currency === 'string' ? overview.account_currency : undefined
    };
    
    logger.debug('Successfully converted metrics data to AdMetricsResponse format');
    
    return {
      overview: validatedOverview,
      campaigns: mappedCampaigns,
      daily_metrics: mappedDailyMetrics,
      organic: organicData || undefined // Use the extracted organicData
    };
  }

  /**
   * Get ad metrics with platform breakdown for a page
   * Attempts client-side processing first, then falls back to API.
   * @param pageId Meta page ID
   * @param storeId MongoDB store ID
   * @param timeRange Optional time range for metrics
   * @param platform Optional platform type ('facebook' or 'instagram')
   * @param includeOrganic Include organic data (default: true) - Note: This param might not be used by MetaDataService
   * @param forceRefresh Force refresh from Meta API instead of using cached data (default: false)
   * @param maxDataAge Maximum age of data in hours before refresh is needed (default: 24)
   */
  static async getPageAdMetricsWithPlatformBreakdown(
    pageId: string, 
    storeId: string,
    timeRange?: TimeRange,
    platform?: 'facebook' | 'instagram',
    includeOrganic: boolean = true,
    forceRefresh: boolean = false,
    maxDataAge: number = 24
  ): Promise<AdMetricsResponse | InstagramAdMetrics> {
    try {
      if (platform === 'instagram') {
        try {
          return await MetaDataService.getInstagramCombinedMetrics(pageId, storeId, timeRange);
        } catch (instagramError) {
          logger.warn('Instagram combined metrics failed, falling back to general breakdown method:', instagramError);
        }
      }
      
      // Attempt fetch from MetaDataService (client-side processing)
      logger.debug('Attempting MetaDataService.getPageAdMetricsWithPlatformBreakdown...');
      // Use unknown as an intermediate type for proper type conversion
      const result = await MetaDataService.getPageAdMetricsWithPlatformBreakdown(pageId, storeId, timeRange, platform) as unknown;
      logger.debug('MetaDataService.getPageAdMetricsWithPlatformBreakdown success.');
      
      // Type narrowing to determine the correct type
      // Check for Facebook response structure first
      if (this.isFacebookAdMetricsResponse(result)) {
        return this.convertFacebookAdMetricsToAdMetricsResponse(result);
      }
      
      // Check for Instagram response structure
      if (this.isInstagramAdMetrics(result)) {
        return result;
      }
      
      // Fallback - attempt conversion assuming it's Facebook data
      logger.warn('Unable to determine response type - attempting conversion from Facebook format');
      try {
        // Use type assertion with unknown as intermediate step
        return this.convertFacebookAdMetricsToAdMetricsResponse(result as unknown as FacebookAdMetricsResponse);
      } catch (conversionError) {
        logger.error('Conversion failed:', conversionError);
        throw new Error('Received data could not be converted to a valid metrics format');
      }

    } catch (clientError) {
      logger.warn(`MetaDataService.getPageAdMetricsWithPlatformBreakdown failed (falling back to API): ${clientError instanceof Error ? clientError.message : String(clientError)}`);
      
      try {
        logger.debug(`Falling back to API call via this.getAdMetrics...`);
        return await this.getAdMetrics(pageId, storeId, timeRange, includeOrganic, forceRefresh, maxDataAge);
      } catch (apiError) {
        logger.error('Error fetching ad metrics from API fallback:', apiError);
        throw apiError;
      }
    }
  }

  /**
   * Type guard to check if the object is a FacebookAdMetricsResponse
   */
  private static isFacebookAdMetricsResponse(obj: unknown): obj is FacebookAdMetricsResponse {
    return (
      typeof obj === 'object' && 
      obj !== null && 
      'daily_metrics' in obj && 
      Array.isArray((obj as FacebookAdMetricsResponse).daily_metrics) && 
      (obj as FacebookAdMetricsResponse).daily_metrics.length > 0 && 
      'views' in (obj as FacebookAdMetricsResponse).daily_metrics[0]
    );
  }
  
  /**
   * Type guard to check if the object is an InstagramAdMetrics
   */
  private static isInstagramAdMetrics(obj: unknown): obj is InstagramAdMetrics {
    return (
      typeof obj === 'object' && 
      obj !== null && 
      'ad_metrics' in obj && 
      'organic_metrics' in obj && 
      'combined' in obj && 
      'source' in obj
    );
  }

  /**
   * Get ad metrics via API endpoint
   * @param pageId Meta page ID
   * @param storeId MongoDB store ID
   * @param timeRange Optional time range for metrics
   * @param includeOrganic Include organic data (default: true)
   * @param forceRefresh Force refresh from Meta API instead of using cached data (default: false)
   * @param maxDataAge Maximum age of data in hours before refresh is needed (default: 24)
   * @param mongoDbOnly Only fetch from MongoDB, skip Meta API (default: false)
   */
  static async getAdMetrics(
    pageId: string,
    storeId: string,
    timeRange?: TimeRange,
    includeOrganic: boolean = true,
    forceRefresh: boolean = false,
    maxDataAge: number = 24,
    mongoDbOnly: boolean = false
  ): Promise<AdMetricsResponse | InstagramAdMetrics> {
    try {
      // First try to get data from MongoDB unless forceRefresh is true
      if (!forceRefresh) {
        try {
          // getAdMetricsFromMongoDB already returns AdMetricsResponse | InstagramAdMetrics
          const mongoData = await this.getAdMetricsFromMongoDB(pageId, storeId, timeRange);
          if (mongoData) {
            logger.info('Successfully retrieved metrics from MongoDB');
            return mongoData;
          }
        } catch (error) {
          logger.warn('Failed to fetch from MongoDB, will try Meta API:', error);
        }
      }

      if (mongoDbOnly) {
        logger.warn('MongoDB only mode: No data found in MongoDB and API fallback is disabled.');
        throw new Error('No data available in MongoDB and mongoDbOnly is set to true');
      }

      const queryParams = new URLSearchParams();
      queryParams.append('page_id', pageId);
      queryParams.append('store_id', storeId);
      
      if (timeRange) {
        if (timeRange.since) queryParams.append('since', timeRange.since);
        if (timeRange.until) queryParams.append('until', timeRange.until);
      }
      if (includeOrganic) queryParams.append('include_organic', 'true');
      if (maxDataAge) queryParams.append('max_data_age', maxDataAge.toString());

      const response = await fetchWithDeduplication<AdMetricsResponse | InstagramAdMetrics | MongoDBMetricsResponse | FacebookAdMetricsResponseWithOptionals>(
        `/api/meta/ad-metrics?${queryParams.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );

      let processedResponse: AdMetricsResponse | InstagramAdMetrics;

      if (MetaStoreService.isInstagramAdMetrics(response)) {
        processedResponse = response;
      } else if (MetaStoreService.isAdMetricsResponse(response)) {
        processedResponse = response;
      } else if (MetaStoreService.isMongoDBMetricsResponse(response)) {
        if (response.metrics) {
          processedResponse = MetaStoreService.convertFacebookAdMetricsToAdMetricsResponse(response.metrics);
        } else {
          logger.error('MongoDB response is missing "metrics" field in getAdMetrics', response);
          throw new Error('Invalid MongoDB metrics response: missing "metrics" field');
        }
      } else if (MetaStoreService.isFacebookAdMetricsType(response)) {
        processedResponse = MetaStoreService.convertFacebookAdMetricsToAdMetricsResponse(response);
      } else {
        logger.error('Unknown metrics response type in getAdMetrics. Full response:', JSON.stringify(response, null, 2));
        throw new Error('Unknown metrics response type received from API');
      }

      try {
        await this.saveMetricsToMongoDB(pageId, storeId, processedResponse);
        logger.info('Successfully saved metrics to MongoDB');
      } catch (error) {
        logger.warn('Failed to save metrics to MongoDB:', error);
      }

      return processedResponse;
    } catch (error) {
      logger.error('Error fetching ad metrics:', error);
      
      if (!mongoDbOnly) {
        try {
          logger.info('Attempting MongoDB fallback after API failure');
          // getAdMetricsFromMongoDB already returns AdMetricsResponse | InstagramAdMetrics
          return await this.getAdMetricsFromMongoDB(pageId, storeId, timeRange);
        } catch (fallbackError) {
          logger.error('MongoDB fallback also failed:', fallbackError);
          throw error; 
        }
      }
      
      throw error;
    }
  }

  /**
   * Save metrics data to MongoDB for caching
   * @param pageId Meta page ID
   * @param storeId MongoDB store ID
   * @param metrics Metrics data to save
   */
  private static async saveMetricsToMongoDB(
    pageId: string,
    storeId: string,
    metrics: AdMetricsResponse | InstagramAdMetrics
  ): Promise<void> {
    try {
      await fetchWithDeduplication(
        `/api/meta/store-metrics`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            page_id: pageId,
            store_id: storeId,
            metrics: metrics,
            timestamp: new Date().toISOString()
          })
        }
      );
    } catch (error) {
      logger.error('Error saving metrics to MongoDB:', error);
      throw error;
    }
  }

  /**
   * Get ad metrics from MongoDB
   * @param pageId Meta page ID
   * @param storeId MongoDB store ID
   * @param timeRange Optional time range for filtering metrics
   */
  static async getAdMetricsFromMongoDB(
    pageId: string,
    storeId: string,
    timeRange?: TimeRange
  ): Promise<AdMetricsResponse | InstagramAdMetrics> {
    logger.info(`Attempting MongoDB fallback via GET /api/meta/ad-metrics for page ${pageId}...`, { pageId, storeId, timeRange });
    const queryParams = new URLSearchParams();
    queryParams.append('page_id', pageId);
    queryParams.append('store_id', storeId);
    queryParams.append('source', 'mongodb');
    if (timeRange?.since) queryParams.append('since', timeRange.since);
    if (timeRange?.until) queryParams.append('until', timeRange.until);
    try {
      const response = await fetchWithDeduplication<AdMetricsResponse | InstagramAdMetrics | MongoDBMetricsResponse | FacebookAdMetricsResponseWithOptionals | MongoFlatMetric[]>(
        `/api/meta/ad-metrics?${queryParams.toString()}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      // 1. Aggregated MongoDB document (with metrics key or direct keys)
      if (MetaStoreService.isMongoDBMetricsResponse(response)) {
        logger.debug('MongoDB response is aggregated structure.');
        const metrics = response.metrics ? response.metrics : response;
        return MetaStoreService.convertFacebookAdMetricsToAdMetricsResponse(metrics);
      }
      // 2. Array of flat MongoDB documents
      if (isFlatMongoDbMetricsArray(response)) {
        logger.debug('MongoDB response is array of flat metric documents. Aggregating client-side.');
        // Aggregate overview, daily_metrics, campaigns
        const overview = {
          total_spend: 0,
          total_impressions: 0,
          total_reach: 0,
          total_clicks: 0,
          total_conversions: 0,
          average_cpc: 0,
          average_ctr: 0,
          average_cost_per_conversion: 0,
          average_roi: 0,
          roas: 0,
          account_currency: undefined as string | undefined
        };
        const dailyMetricsMap: Record<string, {
          date: string;
          spend: number;
          impressions: number;
          clicks: number;
          conversions: number;
        }> = {};
        const campaignsMap: Record<string, {
          id: string;
          name: string;
          status: string;
          spend: number;
          impressions: number;
          clicks: number;
          conversions: number;
        }> = {};
        let _totalDays = 0;
        response.forEach(doc => {
          // Overview totals
          overview.total_spend += doc.spend || 0;
          overview.total_impressions += doc.impressions || 0;
          overview.total_reach += doc.reach || 0;
          overview.total_clicks += doc.clicks || 0;
          overview.total_conversions += doc.conversions || 0;
          if (!overview.account_currency && doc.currency) overview.account_currency = doc.currency;
          // Daily metrics
          if (doc.date) {
            if (!dailyMetricsMap[doc.date]) {
              dailyMetricsMap[doc.date] = {
                date: doc.date,
                spend: 0,
                impressions: 0,
                clicks: 0,
                conversions: 0
              };
              _totalDays++;
            }
            dailyMetricsMap[doc.date].spend += doc.spend || 0;
            dailyMetricsMap[doc.date].impressions += doc.impressions || 0;
            dailyMetricsMap[doc.date].clicks += doc.clicks || 0;
            dailyMetricsMap[doc.date].conversions += doc.conversions || 0;
          }
          // Campaigns
          if (doc.campaign_id) {
            if (!campaignsMap[doc.campaign_id]) {
              campaignsMap[doc.campaign_id] = {
                id: doc.campaign_id,
                name: doc.campaign_id, // If name is not available, use ID
                status: 'UNKNOWN',
                spend: 0,
                impressions: 0,
                clicks: 0,
                conversions: 0
              };
            }
            campaignsMap[doc.campaign_id].spend += doc.spend || 0;
            campaignsMap[doc.campaign_id].impressions += doc.impressions || 0;
            campaignsMap[doc.campaign_id].clicks += doc.clicks || 0;
            campaignsMap[doc.campaign_id].conversions += doc.conversions || 0;
          }
        });
        // Derived metrics
        overview.average_ctr = overview.total_impressions > 0 ? (overview.total_clicks / overview.total_impressions) * 100 : 0;
        overview.average_cpc = overview.total_clicks > 0 ? overview.total_spend / overview.total_clicks : 0;
        overview.average_cost_per_conversion = overview.total_conversions > 0 ? overview.total_spend / overview.total_conversions : 0;
        overview.roas = overview.total_spend > 0 ? (overview.total_conversions * 50) / overview.total_spend : 0;
        overview.average_roi = overview.total_spend > 0 ? (overview.total_conversions * 100) / overview.total_spend : 0;
        // Convert maps to arrays
        const daily_metrics = Object.values(dailyMetricsMap);
        const campaigns = Object.values(campaignsMap);
        return {
          overview,
          daily_metrics,
          campaigns
        };
      }
      // 3. Already AdMetricsResponse or InstagramAdMetrics
      if (MetaStoreService.isAdMetricsResponse(response)) {
        logger.debug('MongoDB response is already AdMetricsResponse.');
        return response;
      }
      if (MetaStoreService.isInstagramAdMetrics(response)) {
        logger.debug('MongoDB response is InstagramAdMetrics.');
        return response;
      }
      // 4. Unknown structure
      logger.error('No valid metrics structure found in MongoDB response in getAdMetricsFromMongoDB:', response);
      throw new Error('No valid metrics structure found in MongoDB response');
    } catch (error) {
      logger.error('Error fetching metrics from MongoDB:', error);
      throw error;
    }
  }

  static async getCampaignAttributedSales(
    campaignId: string,
    storeId: string,
    startDate: string, // YYYY-MM-DD
    endDate: string // YYYY-MM-DD
  ): Promise<{ campaign_id: string; attributed_sales: number }> {
    const queryParams = new URLSearchParams({
      store_id: storeId,
      since: startDate,
      until: endDate,
    });
    const apiUrl = `/api/meta/campaign/${campaignId}/attributed-sales?${queryParams.toString()}`; // Changed path
    try {
      // Use fetchWithDeduplication for consistency and error handling
      const data = await fetchWithDeduplication<{ campaign_id: string; attributed_sales: number }>(apiUrl, {
        method: 'GET',
      });
      return data;
    } catch (error) {
      logger.error(`Error fetching attributed sales for campaign ${campaignId}:`, error);
      // Re-throw as ApiError or a more specific error if needed by calling code
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to fetch attributed sales', 0, 'Client Error', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Get campaign sales correlation data for a store
   * @param storeId Store ID
   * @param timeRange Optional time range for filtering campaigns and sales data
   * @returns Campaign sales correlation data with enhanced campaigns and sales data
   */
  static async getCampaignSalesCorrelation(
    storeId: string, 
    timeRange?: TimeRange
  ): Promise<{
    enhanced_campaigns: MetaAdCampaign[];
    sales_data: {
        daily_sales: Array<{ date: string; sales?: number; revenue?: number }>;
        product_sales: Array<{ product_id: string; product_name: string; quantity: number; revenue: number }>;
      };
  }> {
    try {
      // Build query parameters with date range if provided
      const queryParams = new URLSearchParams();
      if (timeRange?.since) {
        queryParams.append('since', timeRange.since);
      }
      if (timeRange?.until) {
        queryParams.append('until', timeRange.until);
      }
      
      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
      const url = `/api/meta/${storeId}/campaign-sales-correlation${queryString}`;
      
      logger.info(`Fetching campaign sales correlation for store ${storeId} with time range:`, timeRange);
      
      const data = await fetchWithDeduplication<{
        enhanced_campaigns: MetaAdCampaign[];
        sales_data: {
          daily_sales: Array<{ date: string; sales?: number; revenue?: number }>;
          product_sales: Array<{ product_id: string; product_name: string; quantity: number; revenue: number }>;
        };
      }>(url, {
        method: 'GET',
      });
      
      logger.info(`Successfully fetched campaign sales correlation for store ${storeId}`);
      logger.debug(`Found ${data.enhanced_campaigns?.length || 0} enhanced campaigns`);
      logger.debug(`Found ${data.sales_data?.daily_sales?.length || 0} daily sales records`);
      
      return data;
    } catch (error) {
      logger.error(`Error fetching campaign sales correlation for store ${storeId}:`, error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to fetch campaign sales correlation', 0, 'Client Error', error instanceof Error ? error : undefined);
    }
  }

  /**
   * Trigger an update of sales correlation data
   * @param storeId Store ID
   * @returns Promise indicating the update was triggered
   */
  static async triggerSalesCorrelationUpdate(storeId: string): Promise<{ message: string }> {
    try {
      const data = await fetchWithDeduplication<{ message: string }>(`/api/meta/${storeId}/update-sales-correlation`, {
        method: 'POST',
      });
      
      logger.info(`Successfully triggered sales correlation update for store ${storeId}`);
      return data;
    } catch (error) {
      logger.error(`Error triggering sales correlation update for store ${storeId}:`, error);
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError('Failed to trigger sales correlation update', 0, 'Client Error', error instanceof Error ? error : undefined);
    }
  }

  static isAdMetricsResponse(response: unknown): response is AdMetricsResponse {
    logger.debug('Checking if response is AdMetricsResponse:', response);
    return (
      typeof response === 'object' &&
      response !== null &&
      'overview' in response &&
      'campaigns' in response &&
      'daily_metrics' in response
    );
  }

  static isFacebookAdMetricsType(response: unknown): response is FacebookAdMetricsResponseWithOptionals {
    logger.debug('Checking if response is FacebookAdMetricsType:', response);
    return (
      typeof response === 'object' &&
      response !== null &&
      ('spend' in response || 'impressions' in response || 'clicks' in response)
    );
  }

  private static isMongoDBMetricsResponse(response: unknown): response is MongoDBMetricsResponse {
    logger.debug('Checking if response is MongoDBMetricsResponse:', response);
    // Check for the presence of a metrics key with overview, campaigns, and daily_metrics
    if (typeof response === 'object' && response !== null) {
      const resp = response as { metrics?: unknown };
      if (
        resp.metrics &&
        typeof resp.metrics === 'object' &&
        resp.metrics !== null &&
        'overview' in resp.metrics &&
        'campaigns' in resp.metrics &&
        'daily_metrics' in resp.metrics
      ) {
        logger.debug('Response is MongoDBMetricsResponse (with metrics key)');
        return true;
      }
      // Also check for direct overview/campaigns/daily_metrics at the root
      if (
        'overview' in response &&
        'campaigns' in response &&
        'daily_metrics' in response
      ) {
        logger.debug('Response is MongoDBMetricsResponse (direct keys at root)');
        return true;
      }
    }
    return false;
  }
}