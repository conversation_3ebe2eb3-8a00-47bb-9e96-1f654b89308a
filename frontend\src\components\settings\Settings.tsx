import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, TextField, Button, Typography, Paper, CircularProgress, Alert, Snackbar, Switch, FormControlLabel, Divider, FormControl, InputLabel, Select, MenuItem, List, ListItem, ListItemText, IconButton, Skeleton, useMediaQuery, useTheme } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useThemeContext } from '../../contexts/ThemeContext';
import NavigationWrapper from '../common/NavigationWrapper';
import PageContainer from '../common/PageContainer';
import { SecurityOutlined, CheckCircle, Cancel, DeleteOutline, Email as EmailIcon, Palette as PaletteIcon, Language as LanguageIcon, VpnKey as KeyIcon } from '@mui/icons-material';
import { storeService, CompanyProfile } from '../../services/storeService';
import { fetchWithDeduplication, invalidateCache } from '../../services/apiService';
import { API_URL } from "../../config/api";
import { SelectChangeEvent } from '@mui/material';
import { useTranslation } from 'react-i18next';
import LanguageSelector from '../common/LanguageSelector';
import CookieManagementCard from './CookieManagementCard';
import { authService } from '../../services/authService';
import { validateEmail, validateCompanyName } from '../../utils/validation';
import { logger } from '../../utils/logger';
import { useCsrfErrorRecovery } from '../../hooks/useCsrfErrorRecovery';

// Define error response type to replace 'any'
type ApiErrorResponse = {
  response?: {
    data?: {
      detail?: string | Record<string, unknown>;
      message?: string;
    };
    status?: number;
  };
  message?: string;
};

// Define Alert2FA type
interface Alert2FA {
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
}

const Settings: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { mode, toggleColorMode } = useThemeContext();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { retryRequest, isRecovering, lastError, clearError } = useCsrfErrorRecovery();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [profile, setProfile] = useState<CompanyProfile>({
    company_name: '',
    business_type: '',
    contact_name: '',
    tax_id: '',
    dni: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    alternate_emails: [],
    selected_meta_email: null
  });

  // 2FA States
  const [is2FAEnabled, setIs2FAEnabled] = useState<boolean>(false);
  const [isLoading2FA, setIsLoading2FA] = useState<boolean>(false);
  const [alert2FA, setAlert2FA] = useState<Alert2FA | null>(null);
  const [showVerification, setShowVerification] = useState<boolean>(false);
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [emailSendingState, setEmailSendingState] = useState<'idle' | 'sending' | 'sent'>('idle');

  // Password change states
  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [isLoadingPassword, setIsLoadingPassword] = useState(false);
  
  // Password validation states
  const [lengthValid, setLengthValid] = useState(false);
  const [uppercaseValid, setUppercaseValid] = useState(false);
  const [numberValid, setNumberValid] = useState(false);
  const [symbolValid, setSymbolValid] = useState(false);
  const [passwordsMatch, setPasswordsMatch] = useState(false);
  
  // Use a ref to track if the profile has been fetched
  const profileFetchedRef = useRef(false);

  // Define a skeleton height for mobile to prevent layout shift
  const skeletonHeight = 340;

  // Meta email states
  const [selectedMetaEmail, setSelectedMetaEmail] = useState<string>('');
  const [isLoadingMetaEmail, setIsLoadingMetaEmail] = useState<boolean>(false);
  const [metaEmailSuccess, setMetaEmailSuccess] = useState<string | null>(null);
  const [metaEmailError, setMetaEmailError] = useState<string | null>(null);

  // Alternate email states
  const [newAlternateEmail, setNewAlternateEmail] = useState('');
  const [alternateEmailError, setAlternateEmailError] = useState<string | null>(null);
  const [isLoadingAlternateEmail, setIsLoadingAlternateEmail] = useState(false);
  const [alternateEmailSuccess, setAlternateEmailSuccess] = useState<string | null>(null);

  // Company profile validation states
  const [profileErrors, setProfileErrors] = useState({
    company_name: '',
    contact_name: '',
    business_type: '',
    email: '',
    tax_id: '',
    phone: '',
    dni: '',
    website: '',
    address: ''
  });

  // Alternate email validation
  const [alternateEmailValidationError, setAlternateEmailValidationError] = useState('');

  // 2FA verification code validation
  const [verificationCodeError, setVerificationCodeError] = useState('');

  const isAdmin = user?.role === 'admin';

  // Validation functions

  const validateContactName = (value: string): string => {
    if (!value.trim()) return t('settings.validation.contactNameRequired', 'Contact name is required');
    if (value.length < 2) return t('settings.validation.contactNameTooShort', 'Contact name must be at least 2 characters');
    if (value.length > 50) return t('settings.validation.contactNameTooLong', 'Contact name must not exceed 50 characters');
    if (!/^[a-zA-Z\s'-]+$/.test(value)) return t('settings.validation.contactNameInvalidChars', 'Contact name can only contain letters, spaces, hyphens, and apostrophes');
    return '';
  };

  const validateBusinessType = (value: string): string => {
    if (!value.trim()) return t('settings.validation.businessTypeRequired', 'Business type is required');
    if (value.length < 2) return t('settings.validation.businessTypeTooShort', 'Business type must be at least 2 characters');
    if (value.length > 50) return t('settings.validation.businessTypeTooLong', 'Business type must not exceed 50 characters');
    if (!/^[a-zA-Z\s-]+$/.test(value)) return t('settings.validation.businessTypeInvalidChars', 'Business type can only contain letters, spaces, and hyphens');
    return '';
  };



  const validateTaxId = (value: string): string => {
    if (!value.trim()) return t('settings.validation.taxIdRequired', 'Tax ID is required');
    if (value.length < 8) return t('settings.validation.taxIdTooShort', 'Tax ID must be at least 8 characters');
    if (value.length > 20) return t('settings.validation.taxIdTooLong', 'Tax ID must not exceed 20 characters');
    if (!/^[a-zA-Z0-9-]+$/.test(value)) return t('settings.validation.taxIdInvalidChars', 'Tax ID can only contain letters, numbers, and hyphens');
    return '';
  };

  const validatePhone = (value: string): string => {
    if (!value.trim()) return t('settings.validation.phoneRequired', 'Phone number is required');
    if (value.length < 10) return t('settings.validation.phoneTooShort', 'Phone number must be at least 10 characters');
    if (value.length > 18) return t('settings.validation.phoneTooLong', 'Phone number must not exceed 18 characters');
    if (!/^[+]?[0-9\s()-]+$/.test(value)) return t('settings.validation.phoneInvalid', 'Please enter a valid phone number');
    return '';
  };

  const validateDni = (value: string): string => {
    if (!value.trim()) return t('settings.validation.dniRequired', 'DNI is required');
    if (value.length < 7) return t('settings.validation.dniTooShort', 'DNI must be at least 7 characters');
    if (value.length > 12) return t('settings.validation.dniTooLong', 'DNI must not exceed 12 characters');
    if (!/^[a-zA-Z0-9]+$/.test(value)) return t('settings.validation.dniInvalidChars', 'DNI can only contain letters and numbers');
    return '';
  };

  const validateWebsite = (value: string): string => {
    if (!value.trim()) return t('settings.validation.websiteRequired', 'Website is required');
    if (value.length > 255) return t('settings.validation.websiteTooLong', 'Website URL must not exceed 255 characters');
    if (!/^https?:\/\/.+\..+/.test(value)) return t('settings.validation.websiteInvalid', 'Please enter a valid website URL (including http:// or https://)');
    return '';
  };

  const validateAddress = (value: string): string => {
    if (!value.trim()) return t('settings.validation.addressRequired', 'Address is required');
    if (value.length < 5) return t('settings.validation.addressTooShort', 'Address must be at least 5 characters');
    if (value.length > 200) return t('settings.validation.addressTooLong', 'Address must not exceed 200 characters');
    if (!/^[\p{L}0-9\s.,#/\-ºª]+$/u.test(value)) return t('settings.validation.addressInvalidChars', 'Address contains invalid characters');
    return '';
  };

  const validateAlternateEmail = validateEmail; // Use shared validation utility

  const validate2FACode = (value: string): string => {
    if (!value.trim()) return t('settings.validation.codeRequired', 'Verification code is required');
    if (!/^\d{6}$/.test(value)) return t('settings.validation.codeInvalid', 'Verification code must be exactly 6 digits');
    return '';
  };

  // Validation handlers
  const handleProfileFieldValidation = (field: keyof typeof profileErrors) => (value: string) => {
    let error = '';
    switch (field) {
      case 'company_name':
        error = validateCompanyName(value);
        break;
      case 'contact_name':
        error = validateContactName(value);
        break;
      case 'business_type':
        error = validateBusinessType(value);
        break;
      case 'email':
        error = validateEmail(value);
        break;
      case 'tax_id':
        error = validateTaxId(value);
        break;
      case 'phone':
        error = validatePhone(value);
        break;
      case 'dni':
        error = validateDni(value);
        break;
      case 'website':
        error = validateWebsite(value);
        break;
      case 'address':
        error = validateAddress(value);
        break;
    }
    setProfileErrors(prev => ({ ...prev, [field]: error }));
  };

  // Memoized fetchProfile function
  const fetchProfile = useCallback(async () => {
    if (isAdmin) {
      setLoading(false);
      return;
    }
    if (!user?.id_store) {
      logger.debug('No store ID available in Settings');
      setLoading(false);
      setError(t('settings.errorNoStoreId'));
      return;
    }
    if (profileFetchedRef.current) {
      logger.debug('Profile already fetched, skipping duplicate fetch');
      return;
    }
    setLoading(true);
    setError(null);
    try {
      logger.debug('Fetching profile for store ID:', user.id_store);
      const data = await storeService.getCompanyProfile(user.id_store);
      logger.debug('Received profile data:', data);
      setProfile(data);
      profileFetchedRef.current = true;
    } catch (error) {
      logger.error('Failed to fetch profile:', error);
      setError(t('settings.errorLoadProfileFailed'));
    } finally {
      setLoading(false);
    }
  }, [user?.id_store, t, isAdmin]);

  // Use a more specific dependency array for the useEffect
  useEffect(() => {
    if (user && user.role !== 'admin' && user.id_store && !profileFetchedRef.current) {
      fetchProfile();
    }
  }, [user?.id_store, fetchProfile, t, user]);

  useEffect(() => {
    if (isAdmin) {
      setLoading(false);
      // Clear the fetched flag so profile can refresh correctly if role changes back
      profileFetchedRef.current = false;
    }
  }, [isAdmin]);

  // Check if 2FA is enabled on component mount
  useEffect(() => {
    const authToken = authService.getToken();

    const checkStatus = async () => {
      if (!user?.email) return;

      try {
        logger.debug('Checking 2FA status for user:', user.email);
        // Use fetchWithDeduplication instead of direct fetch
        const data = await fetchWithDeduplication<{ enabled: boolean }>(`${API_URL}/api/auth/2fa/status/${user.email}`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        logger.debug('2FA status response:', data);
        // Always update the state with the server response
        setIs2FAEnabled(data.enabled);
      } catch (error) {
        logger.error('Error checking 2FA status:', error);
      }
    };

    if (user?.email) {
      // Check status on mount
      checkStatus();
    }
  }, [user?.email]);

  // Function to refresh 2FA status from the server
  const refresh2FAStatus = useCallback(async () => {
    if (!user?.email) return false;
    
    setIsLoading2FA(true);
    try {
      const statusResponse = await fetchWithDeduplication<{ enabled: boolean }>(
        `${API_URL}/api/auth/2fa/status/${user?.email}`,
        {
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`
          }
        }
      );
      
      logger.debug('Refreshed 2FA status:', statusResponse);
      // Always update the state with the server response
      setIs2FAEnabled(statusResponse.enabled);
      return statusResponse.enabled;
    } catch (error: unknown) {
      logger.error('Error refreshing 2FA status:', error);
      return false;
    } finally {
      setIsLoading2FA(false);
    }
  }, [user?.email]);

  // Update validation states as user types
  const validatePasswordRequirements = (password: string) => {
    setLengthValid(password.length >= 8);
    setUppercaseValid(/[A-Z]/.test(password));
    setNumberValid(/[0-9]/.test(password));
    setSymbolValid(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password));
  };

  // Check if passwords match
  useEffect(() => {
    setPasswordsMatch(newPassword === confirmPassword && newPassword !== '');
  }, [newPassword, confirmPassword]);

  // Validate password as user types
  useEffect(() => {
    validatePasswordRequirements(newPassword);
  }, [newPassword]);

  // Set initial selected email when profile is fetched
  useEffect(() => {
    if (profile.selected_meta_email) {
      setSelectedMetaEmail(profile.selected_meta_email);
    } else if (profile.email) {
      setSelectedMetaEmail(profile.email);
    }
  }, [profile.selected_meta_email, profile.email]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (isAdmin) {
      return;
    }
    
    // Validate all fields before submission
    const companyNameError = validateCompanyName(profile.company_name);
    const contactNameError = validateContactName(profile.contact_name);
    const businessTypeError = validateBusinessType(profile.business_type);
    const emailError = validateEmail(profile.email);
    const taxIdError = validateTaxId(profile.tax_id);
    const phoneError = validatePhone(profile.phone);
    const dniError = validateDni(profile.dni);
    const websiteError = validateWebsite(profile.website);
    const addressError = validateAddress(profile.address);

    const newProfileErrors = {
      company_name: companyNameError,
      contact_name: contactNameError,
      business_type: businessTypeError,
      email: emailError,
      tax_id: taxIdError,
      phone: phoneError,
      dni: dniError,
      website: websiteError,
      address: addressError
    };

    setProfileErrors(newProfileErrors);

    // Check if there are any validation errors
    const hasErrors = Object.values(newProfileErrors).some(error => error !== '');
    if (hasErrors) {
      setError(t('settings.companyProfile.validationError', 'Please fix the validation errors before submitting'));
      return;
    }

    if (!user?.id_store) {
      setError(t('settings.errorNoStoreId'));
      return;
    }

    setError(null);
    setSuccessMessage(null);
    clearError(); // Clear any previous CSRF errors
    
    try {
      logger.debug('Submitting profile update:', profile);
      
      // Use retryRequest with fetchWithDeduplication for CSRF error recovery
      await retryRequest(async () => {
        return fetchWithDeduplication<{ message: string }>(`${API_URL}/api/store/${user.id_store}/company-profile`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            company_name: profile.company_name,
            business_type: profile.business_type,
            contact_name: profile.contact_name,
            tax_id: profile.tax_id,
            dni: profile.dni,
            phone: profile.phone,
            address: profile.address,
            website: profile.website
          })
        });
      });
      
      logger.info('Update successful');
      setSuccessMessage(t('settings.companyProfile.updateSuccess'));
      
      // Invalidate the company profile cache since we've updated the profile
      invalidateCache(`/api/store/${user.id_store}/company-profile`);
    } catch (error) {
      logger.error('Error updating profile:', error);
      // Show CSRF-specific error if available, otherwise show generic error
      setError(lastError || t('settings.companyProfile.updateError'));
    }
  };

  const handle2FAToggle = async () => {
    if (!user?.email) return;
    
    setIsLoading2FA(true);
    setAlert2FA(null);
    setEmailSendingState('sending');
    clearError(); // Clear any previous CSRF errors
    
    try {
      const currentStatus = is2FAEnabled; // Use current local state to avoid race conditions
      const endpoint = !currentStatus ? '/api/auth/2fa/enable' : '/api/auth/2fa/disable';
      logger.debug(`Toggling 2FA: Current status is ${currentStatus ? 'enabled' : 'disabled'}, using endpoint ${endpoint}`);
      
      await retryRequest(async () => {
        return fetchWithDeduplication<{ message: string }>(`${API_URL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Content-Type': 'application/json'
          }
        });
      });

      setShowVerification(true);
      setEmailSendingState('sent');
      setAlert2FA({ 
        type: 'info', 
        message: t(`settings.twoFactor.${!currentStatus ? 'infoEnable' : 'infoDisable'}`, { 
          defaultValue: `Please check your email for the verification code to ${!currentStatus ? 'enable' : 'disable'} 2FA.` 
        })
      });
    } catch (error: unknown) {
      const apiError = error as ApiErrorResponse;
      logger.error('Error toggling 2FA:', apiError);
      let errorMessage = lastError || 'An error occurred. Please try again.';
      
      // Differentiate between different error types for better user feedback
      if (apiError.response?.data?.detail) {
        const detail = typeof apiError.response.data.detail === 'string' 
          ? apiError.response.data.detail 
          : JSON.stringify(apiError.response.data.detail);
        
        // Check for authentication errors
        if (detail.toLowerCase().includes('authentication') || 
            detail.toLowerCase().includes('unauthorized') ||
            detail.toLowerCase().includes('invalid token')) {
          errorMessage = 'Authentication error. Please refresh the page and try again.';
        }
        // Check for CSRF errors
        else if (detail.toLowerCase().includes('csrf') || 
                 detail.toLowerCase().includes('token') ||
                 detail.toLowerCase().includes('expired')) {
          errorMessage = 'Security token error. The page will refresh automatically.';
          // Auto-refresh after a short delay for CSRF errors
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        }
        // Use the server error message for other cases
        else {
          errorMessage = detail;
        }
      }
      // Handle cases where lastError is set (from CSRF recovery)
      else if (lastError) {
        if (lastError.toLowerCase().includes('authentication')) {
          errorMessage = 'Authentication error. Please refresh the page and try again.';
        } else if (lastError.toLowerCase().includes('csrf') || 
                   lastError.toLowerCase().includes('token')) {
          errorMessage = 'Security token error. The page will refresh automatically.';
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        }
      }
      
      setAlert2FA({ 
        type: 'error', 
        message: errorMessage
      });
      
      refresh2FAStatus();
      setEmailSendingState('idle');
    } finally {
      setIsLoading2FA(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!user?.email || !verificationCode) return;
    
    setIsLoading2FA(true);
    setAlert2FA(null);
    clearError(); // Clear any previous CSRF errors
    
    try {
      const currentStatus = await refresh2FAStatus();
      const action = currentStatus ? "disable" : "enable";
      logger.debug(`Verifying 2FA code with action: ${action}`);
      
      await retryRequest(async () => {
        return fetchWithDeduplication<{ message: string }>(`${API_URL}/api/auth/2fa/verify`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authService.getToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: user.email,
            code: verificationCode,
            action: action
          })
        });
      });

      setShowVerification(false);
      setVerificationCode('');
      invalidateCache(`/api/auth/2fa/status/${user.email}`);
      const updatedStatus = await refresh2FAStatus();
      
      setAlert2FA({ 
        type: 'success', 
        message: t(`settings.twoFactor.${updatedStatus ? 'successEnabled' : 'successDisabled'}`, { 
          defaultValue: `2FA has been ${updatedStatus ? 'enabled' : 'disabled'} successfully.` 
        })
      });
      
    } catch (error: unknown) {
      const apiError = error as ApiErrorResponse;
      logger.error('Error verifying 2FA code:', apiError);
      let errorMessage = lastError || 'Invalid verification code';
      if (apiError.response?.data?.detail) {
        errorMessage = typeof apiError.response.data.detail === 'string' 
          ? apiError.response.data.detail 
          : JSON.stringify(apiError.response.data.detail);
      }
      
      setAlert2FA({ 
        type: 'error', 
        message: errorMessage
      });
      
      refresh2FAStatus();
    } finally {
      setIsLoading2FA(false);
    }
  };

  const handleChange = (field: keyof CompanyProfile) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setProfile(prev => ({ ...prev, [field]: value }));
    
    // Clear any existing error and success messages
    setError(null);
    setSuccessMessage(null);
    
    // Only trigger validation for fields that have validation (not meta-specific fields)
    const validatedFields: (keyof typeof profileErrors)[] = [
      'company_name', 'contact_name', 'business_type', 'email', 'tax_id', 'phone', 'dni', 'website', 'address'
    ];
    
    if (validatedFields.includes(field as keyof typeof profileErrors)) {
      handleProfileFieldValidation(field as keyof typeof profileErrors)(value);
    }
  };

  const handlePasswordChange = async (event: React.FormEvent) => {
    event.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);
    setIsLoadingPassword(true);
    clearError(); // Clear any previous CSRF errors

    try {
      if (newPassword.length < 8 || 
          !/[A-Z]/.test(newPassword) || 
          !/[0-9]/.test(newPassword) ||
          !/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(newPassword)) {
        setPasswordError(t('settings.password.validationRequirements', 'Password must be at least 8 characters long, contain at least one uppercase letter, one number, and one symbol'));
        setIsLoadingPassword(false);
        return;
      }

      if (newPassword !== confirmPassword) {
        setPasswordError(t('settings.password.mismatch', 'New passwords do not match'));
        setIsLoadingPassword(false);
        return;
      }

      await retryRequest(async () => {
        return fetchWithDeduplication<{ message: string }>(`${API_URL}/api/auth/change-password`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authService.getToken()}`,
          },
          body: JSON.stringify({
            old_password: oldPassword,
            new_password: newPassword,
            confirm_password: confirmPassword,
          }),
        });
      });

      setPasswordSuccess(t('settings.password.changeSuccess', 'Password changed successfully'));
      setOldPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: unknown) {
      const apiError = error as ApiErrorResponse;
      const errorDetail = apiError.response?.data?.detail;
      setPasswordError(lastError || (typeof errorDetail === 'string' ? errorDetail : 'Failed to change password'));
    } finally {
      setIsLoadingPassword(false);
    }
  };

  const handleMetaEmailChange = (event: SelectChangeEvent<string>) => {
    setSelectedMetaEmail(event.target.value);
  };

  const handleSetMetaEmail = async () => {
    if (!user?.id_store) return;
    
    setIsLoadingMetaEmail(true);
    setMetaEmailError(null);
    setMetaEmailSuccess(null);
    
    try {
      logger.debug(`Updating Meta email to: ${selectedMetaEmail} for store ID: ${user.id_store}`);
      logger.debug(`User context email: ${user.email}`);
      
      const response = await storeService.selectMetaEmail(
        user.id_store,
        selectedMetaEmail
      );
      
      logger.debug('Meta email update response:', response);
      
      // Use the response to check if the operation was successful
      if (response && response.selected_email) {
        logger.info(`Meta email update successful, new selected email: ${response.selected_email}`);
        setMetaEmailSuccess(t('settings.metaEmail.updateSuccess', 'Meta login email updated successfully'));
        setProfile({
          ...profile,
          selected_meta_email: selectedMetaEmail
        });
        
        // Set a flag in localStorage to indicate that the Meta email has been updated
        // This will be used by the MetaDashboard to refresh the connection
        localStorage.setItem('meta_email_updated', 'true');
        
        // Invalidate company profile cache
        logger.debug(`Invalidating cache for: /api/store/${user.id_store}/company-profile`);
        invalidateCache(`/api/store/${user.id_store}/company-profile`);
      } else {
        const message = response?.message || t('settings.metaEmail.updateFailed', 'Failed to update Meta login email. Please try again.');
        logger.error('Meta email update failed with message:', message);
        setMetaEmailError(message);
      }
    } catch (error) {
      logger.error('Error updating Meta email:', error);
      setMetaEmailError(t('settings.metaEmail.updateFailed', 'Failed to update Meta login email. Please try again.'));
    } finally {
      setIsLoadingMetaEmail(false);
    }
  };

  const handleAddAlternateEmail = async () => {
    if (isAdmin) return;
    
    // Validate the new alternate email
    const emailError = validateAlternateEmail(newAlternateEmail);
    setAlternateEmailValidationError(emailError);
    
    if (emailError) {
      setAlternateEmailError(emailError);
      return;
    }
    
    if (!user?.id_store) {
      setAlternateEmailError(t('settings.errorNoStoreId'));
      return;
    }
    
    // Check if email already exists
    if (profile.email === newAlternateEmail || profile.alternate_emails?.includes(newAlternateEmail)) {
      setAlternateEmailError(t('settings.metaEmail.emailAlreadyExists', 'This email is already in use'));
      return;
    }
    
    setIsLoadingAlternateEmail(true);
    setAlternateEmailError(null);
    setAlternateEmailSuccess(null);
    
    try {
      // Use the dedicated alternate email API endpoint
      const response = await storeService.addAlternateEmail(user.id_store, newAlternateEmail);
      
      // Update local profile state with the new alternate email
      const updatedProfile = {
        ...profile,
        alternate_emails: [...(profile.alternate_emails || []), newAlternateEmail],
        selected_meta_email: response.selected_email // Use the response from backend
      };
      
      setProfile(updatedProfile);
      setNewAlternateEmail('');
      setAlternateEmailValidationError('');
      setAlternateEmailSuccess(response.message || t('settings.metaEmail.emailAddedSuccess', 'Email added successfully'));
      
      // Update the selected meta email state to match the backend response
      setSelectedMetaEmail(response.selected_email);
      
      // Invalidate cache to ensure fresh data on next fetch
      invalidateCache(`/api/store/${user.id_store}/company-profile`);
      
      // Refresh the profile from the server to ensure UI matches database state
      try {
        const refreshedProfile = await storeService.getCompanyProfile(user.id_store);
        setProfile(refreshedProfile);
        setSelectedMetaEmail(refreshedProfile.selected_meta_email || refreshedProfile.email);
        logger.debug('Profile refreshed after adding alternate email:', refreshedProfile);
      } catch (refreshError) {
        logger.warn('Failed to refresh profile after adding alternate email:', refreshError);
        // Don't throw error here as the main operation succeeded
      }
    } catch (error: unknown) {
      const apiError = error as ApiErrorResponse;
      logger.error('Failed to add alternate email:', error);
      if (apiError.response?.data?.detail) {
        if (typeof apiError.response.data.detail === 'string') {
          setAlternateEmailError(apiError.response.data.detail);
        } else {
          setAlternateEmailError(t('settings.metaEmail.addEmailError', 'Failed to add email'));
        }
      } else {
        setAlternateEmailError(t('settings.metaEmail.addEmailError', 'Failed to add email'));
      }
    } finally {
      setIsLoadingAlternateEmail(false);
    }
  };

  const handleRemoveAlternateEmail = async (email: string) => {
    if (!user?.id_store) return;
    
    setIsLoadingMetaEmail(true);
    setMetaEmailError(null);
    setMetaEmailSuccess(null);
    
    try {
      const response = await storeService.removeAlternateEmail(
        user.id_store,
        email
      );
      
      if (response && response.selected_email) {
        setMetaEmailSuccess(t('settings.metaEmail.alternateRemoved', 'Alternate email removed successfully'));
        setProfile({
          ...profile,
          alternate_emails: (profile.alternate_emails || []).filter((e) => e !== email),
          selected_meta_email: response.selected_email
        });
        
        // Invalidate company profile cache
        invalidateCache(`/api/store/${user.id_store}/company-profile`);
      } else {
        const message = response?.message || t('settings.metaEmail.removeFailed', 'Failed to remove alternate email. Please try again.');
        setMetaEmailError(message);
      }
    } catch (error) {
      logger.error('Error removing alternate email:', error);
      setMetaEmailError(t('settings.metaEmail.removeFailed', 'Failed to remove alternate email. Please try again.'));
    } finally {
      setIsLoadingMetaEmail(false);
    }
  };

  return (
    <Box sx={{ minHeight: '100vh' }}>
      <NavigationWrapper />
      <PageContainer>
        <Box sx={{ py: 4 }} className="px-4 sm:px-6 lg:px-8 space-y-6 sm:space-y-8 pb-24 sm:pb-0">
          <Typography 
            variant="h4" 
            component="h1" 
            gutterBottom
            sx={{
              fontWeight: 700,
              color: '#00A3FF',
              letterSpacing: '1px',
              textTransform: 'uppercase',
            }}
          >
            {t('settings.pageTitle')}
          </Typography>
          
          {loading && !isMobile ? (
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 8 }}>
              <CircularProgress sx={{ color: '#00A3FF' }} />
            </Box>
          ) : error ? (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          ) : null}
          
          <Paper elevation={1} className="bg-gray-800 rounded-lg shadow-md" sx={{ mb: 4, p: 3, bgcolor: 'background.paper', overflowX:{ xs: 'visible', sm: 'visible', md: 'hidden' } }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <SecurityOutlined sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.companyProfile.title')}</Typography>
            </Box>
            
            <Divider sx={{ mb: 3 }} />
            
            {isAdmin && (
              <Alert severity="info" sx={{ mb: 2 }}>
                {t('settings.adminStoreSpecificWarning', 'Company profile settings are store-specific and cannot be managed here.')}
              </Alert>
            )}
            
            {loading && isMobile ? (
              <Skeleton variant="rectangular" animation="wave" sx={{ width: '100%', minHeight: skeletonHeight, borderRadius: '8px' }} />
            ) : !loading && (
              <Box id="profileContent" component="form" onSubmit={handleSubmit} sx={{ 
                display: 'grid', 
                gridTemplateColumns: { xs: '1fr', sm: '1fr', md: '1fr', lg: '1fr 1fr' },
                gap: 2,
                px: { xs: 1, md: 0 }
              }}>
                <TextField
                  label={t('settings.companyProfile.companyNameLabel', 'Company Name')}
                  value={profile.company_name}
                  onChange={handleChange('company_name')}
                  onBlur={() => handleProfileFieldValidation('company_name')(profile.company_name)}
                  fullWidth
                  required
                  disabled={isAdmin}
                  autoComplete="organization"
                  error={!!profileErrors.company_name}
                  helperText={profileErrors.company_name}
                  inputProps={{
                    maxLength: 100
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.contactNameLabel', 'Contact Name')}
                  value={profile.contact_name}
                  onChange={handleChange('contact_name')}
                  onBlur={() => handleProfileFieldValidation('contact_name')(profile.contact_name)}
                  fullWidth
                  disabled={isAdmin}
                  autoComplete="name"
                  error={!!profileErrors.contact_name}
                  helperText={profileErrors.contact_name}
                  inputProps={{
                    maxLength: 50
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.businessTypeLabel', 'Business Type')}
                  value={profile.business_type}
                  onChange={handleChange('business_type')}
                  onBlur={() => handleProfileFieldValidation('business_type')(profile.business_type)}
                  fullWidth
                  required
                  disabled={isAdmin}
                  autoComplete="off"
                  error={!!profileErrors.business_type}
                  helperText={profileErrors.business_type}
                  inputProps={{
                    maxLength: 50
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.emailLabel', 'Email')}
                  value={profile.email}
                  disabled
                  fullWidth
                  autoComplete="email"
                  error={!!profileErrors.email}
                  helperText={profileErrors.email}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&.Mui-disabled': {
                        // backgroundColor: '#f8f9fa', // Removed hardcoded background
                      },
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.taxIdLabel', 'Tax ID')}
                  value={profile.tax_id}
                  onChange={handleChange('tax_id')}
                  onBlur={() => handleProfileFieldValidation('tax_id')(profile.tax_id)}
                  fullWidth
                  disabled={isAdmin}
                  autoComplete="off"
                  error={!!profileErrors.tax_id}
                  helperText={profileErrors.tax_id}
                  inputProps={{
                    maxLength: 20
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.phoneLabel', 'Phone')}
                  value={profile.phone}
                  onChange={handleChange('phone')}
                  onBlur={() => handleProfileFieldValidation('phone')(profile.phone)}
                  fullWidth
                  disabled={isAdmin}
                  autoComplete="tel"
                  error={!!profileErrors.phone}
                  helperText={profileErrors.phone}
                  inputProps={{
                    maxLength: 18
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.dniLabel', 'DNI')}
                  value={profile.dni}
                  onChange={handleChange('dni')}
                  onBlur={() => handleProfileFieldValidation('dni')(profile.dni)}
                  fullWidth
                  disabled={isAdmin}
                  autoComplete="off"
                  error={!!profileErrors.dni}
                  helperText={profileErrors.dni}
                  inputProps={{
                    maxLength: 12
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.websiteLabel', 'Website')}
                  value={profile.website}
                  onChange={handleChange('website')}
                  onBlur={() => handleProfileFieldValidation('website')(profile.website)}
                  fullWidth
                  required
                  disabled={isAdmin}
                  autoComplete="url"
                  error={!!profileErrors.website}
                  helperText={profileErrors.website}
                  inputProps={{
                    maxLength: 255
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                
                <TextField
                  label={t('settings.companyProfile.addressLabel', 'Address')}
                  value={profile.address}
                  onChange={handleChange('address')}
                  onBlur={() => handleProfileFieldValidation('address')(profile.address)}
                  fullWidth
                  multiline
                  rows={2}
                  disabled={isAdmin}
                  autoComplete="street-address"
                  error={!!profileErrors.address}
                  helperText={profileErrors.address}
                  inputProps={{
                    maxLength: 200
                  }}
                  sx={{
                    gridColumn: { xs: 'span 1', lg: 'span 2' },
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />

                <Box sx={{ gridColumn: { xs: 'span 1', lg: 'span 2' }, display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{
                      bgcolor: '#00A3FF',
                      '&:hover': {
                        bgcolor: '#0082CC',
                      },
                    }}
                    disabled={isAdmin}
                  >
                    {t('settings.companyProfile.updateButton')}
                  </Button>
                </Box>
              </Box>
            )}
          </Paper>

          {/* Meta Email Configuration - Separate Section */}
          <Paper elevation={1} className="bg-gray-800 rounded-lg shadow-md" sx={{ mb: 4, p: 3, bgcolor: 'background.paper' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <EmailIcon sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.metaEmail.title', 'Meta Dashboard Email Settings')}</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />

            <Typography variant="body1" sx={{ mb: 3, textAlign: 'center', maxWidth: { xs: '90%', sm: '80%', md: '70%' }, marginX: 'auto' }}>
              {t('settings.metaEmail.description')}
            </Typography>

            {isAdmin && (
              <Alert severity="info" sx={{ mb: 2 }}>
                {t('settings.adminMetaEmailWarning', 'Meta email settings are store-specific and cannot be managed here.')}
              </Alert>
            )}

            <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="subtitle2" gutterBottom>
                {t('settings.alternateEmail.addTitle', 'Add Alternate Email Address')}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {t('settings.alternateEmail.addDescription')}
              </Typography>

              <Box sx={{ display: 'flex', gap: 2, mb: 2, flexDirection: { xs: 'column', sm: 'row' }, alignItems: { xs: 'stretch', sm: 'flex-end' } }}>
                <TextField
                  fullWidth
                  size="small"
                  label={t('settings.alternateEmail.newEmailInputLabel', 'New Email Address')}
                  value={newAlternateEmail}
                  onChange={(e) => setNewAlternateEmail(e.target.value)}
                  onBlur={() => {
                    const error = validateAlternateEmail(newAlternateEmail);
                    setAlternateEmailValidationError(error);
                  }}
                  autoComplete="email"
                  error={!!alternateEmailValidationError || !!alternateEmailError}
                  helperText={alternateEmailValidationError || alternateEmailError}
                  disabled={isAdmin}
                  inputProps={{
                    maxLength: 254
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                />
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleAddAlternateEmail}
                  disabled={isAdmin || isLoadingAlternateEmail}
                  startIcon={isLoadingAlternateEmail ? <CircularProgress size={20} /> : null}
                  fullWidth={true}
                  sx={{ mt: { xs: 1, sm: 0 } }}
                >
                  {t('settings.alternateEmail.addButton')}
                </Button>
              </Box>

              {profile.alternate_emails && profile.alternate_emails.length > 0 ? (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    {t('settings.alternateEmail.yourAlternateEmails', 'Your Alternate Emails:')}
                  </Typography>
                  <List dense>
                    {profile.alternate_emails.map((email, index) => (
                      <ListItem
                        key={index}
                        secondaryAction={
                          <IconButton
                            edge="end"
                            aria-label={t('common.deleteAlt', 'delete')}
                            onClick={() => handleRemoveAlternateEmail(email)}
                            disabled={isAdmin || isLoadingMetaEmail}
                          >
                            <DeleteOutline />
                          </IconButton>
                        }
                      >
                        <ListItemText
                          primary={email}
                          secondary={email === profile.selected_meta_email ? t('settings.alternateEmail.selectedForMeta', 'Selected for Meta') : null}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  {t('settings.alternateEmail.noneAdded')}
                </Typography>
              )}
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                {t('settings.metaEmail.currentSelectionPrefix')}
                <strong> {profile.selected_meta_email || profile.email || 'N/A'}</strong>
              </Typography>
            </Box>

            {(profile.email || (profile.alternate_emails && profile.alternate_emails.length > 0)) ? (
              <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 2, mb: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
                <FormControl fullWidth variant="outlined" size="small">
                  <InputLabel id="meta-email-select-label">{t('settings.metaEmail.selectLabel')}</InputLabel>
                  <Select
                    labelId="meta-email-select-label"
                    id="meta-email-select"
                    value={selectedMetaEmail}
                    onChange={handleMetaEmailChange}
                    label={t('settings.metaEmail.selectLabel')}
                    disabled={isAdmin || isLoadingMetaEmail}
                  >
                    <MenuItem value={profile.email}>{profile.email} {t('settings.metaEmail.primaryLabel', '(Primary)')}</MenuItem>
                    {profile.alternate_emails && profile.alternate_emails.map((email, index) => (
                      <MenuItem key={index} value={email}>{email}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSetMetaEmail}
                  disabled={isAdmin || isLoadingMetaEmail || (profile.selected_meta_email === selectedMetaEmail)}
                  startIcon={isLoadingMetaEmail ? <CircularProgress size={20} /> : null}
                  sx={{ mt: { xs: 1, sm: 0 }, minWidth: { xs: '100%', sm: 'auto' } }}
                >
                  {t('settings.metaEmail.setButton')}
                </Button>
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {t('settings.metaEmail.primaryEmailUsage')}
              </Typography>
            )}

            {metaEmailSuccess && (
              <Alert severity="success" sx={{ mt: 1 }}>
                {metaEmailSuccess}
              </Alert>
            )}

            {metaEmailError && (
              <Alert severity="error" sx={{ mt: 1 }}>
                {metaEmailError}
              </Alert>
            )}

            {alternateEmailSuccess && (
              <Alert severity="success" sx={{ mt: 1 }}>
                {alternateEmailSuccess}
              </Alert>
            )}
          </Paper>
          
          {/* Appearance Settings */}
          <Paper sx={{ p: 3, my: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <PaletteIcon sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.appearance.title')}</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />
            
            <FormControlLabel
              control={
                <Switch
                  checked={mode === 'dark'}
                  onChange={toggleColorMode}
                  name="darkModeSwitch"
                  color="primary"
                />
              }
              label={t('settings.appearance.darkModeLabel')}
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {t('settings.appearance.darkModeDescription')}
            </Typography>
          </Paper>
          
          {/* Language Selection */}
          <Paper sx={{ p: 3, my: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <LanguageIcon sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.language.title', 'Language Settings')}</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />
            
            <LanguageSelector />
          </Paper>
          
          {passwordError && (
            <Alert 
              severity="error" 
              sx={{ 
                mb: 3,
                '& .MuiAlert-message': { 
                  color: '#d32f2f'
                } 
              }}
            >
              {passwordError}
            </Alert>
          )}
          
          {passwordSuccess && (
            <Alert 
              severity="success" 
              sx={{ 
                mb: 3,
                '& .MuiAlert-message': { 
                  color: '#2e7d32'
                } 
              }}
            >
              {passwordSuccess}
            </Alert>
          )}
          
          <Paper elevation={1} className="bg-gray-800 rounded-lg shadow-md" sx={{ mb: 4, p: 3, bgcolor: 'background.paper', overflowX:'hidden' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <KeyIcon sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.changePassword.title', 'Change Your Password')}</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />
            {isAdmin ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                {t('settings.adminPasswordStoreSpecificWarning', 'Password settings are store-specific and cannot be managed here.')}
              </Alert>
            ) : (
              <Box component="form" onSubmit={handlePasswordChange} sx={{ maxWidth: 500 }}>
                <Box sx={{ mb: 4 }}>
                  <Typography variant="body2" color="textSecondary" sx={{ mb: 1 }}>
                    {t('settings.changePassword.requirementsTitle')}
                  </Typography>
                  <Box
                    component="ul"
                    sx={{
                      listStyle: 'none',
                      pl: 0,
                      '& li': {
                        display: 'flex',
                        alignItems: 'center',
                        marginBottom: '8px',
                        color: (theme) => theme.palette.text.secondary,
                        '& .MuiTypography-root': {
                          fontSize: '0.875rem',
                          marginLeft: '8px',
                        },
                      },
                    }}
                  >
                    <li>
                      {lengthValid ? 
                        <CheckCircle sx={{ color: (theme) => theme.palette.success.main, fontSize: '1rem' }} /> : 
                        <Cancel sx={{ color: (theme) => theme.palette.error.main, fontSize: '1rem' }} />}
                      <Typography variant="body2">{t('settings.changePassword.reqLength')}</Typography>
                    </li>
                    <li>
                      {uppercaseValid ? 
                        <CheckCircle sx={{ color: (theme) => theme.palette.success.main, fontSize: '1rem' }} /> : 
                        <Cancel sx={{ color: (theme) => theme.palette.error.main, fontSize: '1rem' }} />}
                      <Typography variant="body2">{t('settings.changePassword.reqUppercase')}</Typography>
                    </li>
                    <li>
                      {numberValid ? 
                        <CheckCircle sx={{ color: (theme) => theme.palette.success.main, fontSize: '1rem' }} /> : 
                        <Cancel sx={{ color: (theme) => theme.palette.error.main, fontSize: '1rem' }} />}
                      <Typography variant="body2">{t('settings.changePassword.reqNumber')}</Typography>
                    </li>
                    <li>
                      {symbolValid ? 
                        <CheckCircle sx={{ color: (theme) => theme.palette.success.main, fontSize: '1rem' }} /> : 
                        <Cancel sx={{ color: (theme) => theme.palette.error.main, fontSize: '1rem' }} />}
                      <Typography variant="body2">{t('settings.changePassword.reqSymbol')}</Typography>
                    </li>
                    {confirmPassword && (
                      <li>
                        {passwordsMatch ? 
                          <CheckCircle sx={{ color: (theme) => theme.palette.success.main, fontSize: '1rem' }} /> : 
                          <Cancel sx={{ color: (theme) => theme.palette.error.main, fontSize: '1rem' }} />}
                        <Typography variant="body2">{t('settings.changePassword.reqMatch')}</Typography>
                      </li>
                    )}
                  </Box>
                </Box>

                <TextField
                  type="password"
                  label={t('settings.changePassword.currentPasswordLabel', 'Current Password')}
                  value={oldPassword}
                  onChange={(e) => setOldPassword(e.target.value)}
                  required
                  fullWidth
                  autoComplete="current-password"
                  inputProps={{
                    maxLength: 128
                  }}
                  className="mb-4"
                />

                <TextField
                  type="password"
                  label={t('settings.changePassword.newPasswordLabel', 'New Password')}
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  fullWidth
                  autoComplete="new-password"
                  inputProps={{
                    maxLength: 128
                  }}
                  className="mb-4"
                />

                <TextField
                  type="password"
                  label={t('settings.changePassword.confirmPasswordLabel', 'Confirm New Password')}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  fullWidth
                  autoComplete="new-password"
                  inputProps={{
                    maxLength: 128
                  }}
                  className="mb-4"
                />

                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isLoadingPassword || isRecovering}
                  className="mt-4"
                  sx={{
                    mt: 3,
                    ...(mode === 'dark' && {
                      bgcolor: '#00A3FF',
                      color: 'white',
                      '&:hover': {
                        bgcolor: '#0082CC',
                      }
                    })
                  }}
                >
                  {(isLoadingPassword || isRecovering) ? <CircularProgress size={24} /> : t('settings.changePassword.changeButton')}
                </Button>
              </Box>
            )}
          </Paper>
          
          {/* Two-Factor Authentication Section */}
          <Paper elevation={1} className="bg-gray-800 rounded-lg shadow-md" sx={{ mb: 4, p: 3, bgcolor: 'background.paper', overflowX:'hidden' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 3 }}>
              <SecurityOutlined sx={{ color: '#00A3FF' }} />
              <Typography variant="h6">{t('settings.twoFactor.title', 'Two-Factor Authentication')}</Typography>
            </Box>
            <Divider sx={{ mb: 3 }} />
            {isAdmin ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                {t('settings.admin2FAStoreSpecificWarning', 'Two-Factor Authentication settings are store-specific and cannot be managed here.')}
              </Alert>
            ) : (
              <Box className="space-y-4">
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={is2FAEnabled}
                        onChange={handle2FAToggle}
                        disabled={isLoading2FA || showVerification}
                        color="primary"
                      />
                    }
                    label={
                      <Box>
                        <Typography>{t('settings.twoFactor.enableSwitchLabel')}</Typography>
                        {emailSendingState === 'sending' && (
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                            <CircularProgress size={12} sx={{ mr: 1 }} />
                            {emailSendingState === 'sending' ? t('settings.twoFactor.resendButtonSending') : emailSendingState === 'sent' ? t('settings.twoFactor.resendButtonSent') : t('settings.twoFactor.resendButtonIdle')}
                          </Typography>
                        )}
                        {emailSendingState === 'sent' && (
                          <Typography variant="caption" color="success.main" sx={{ mt: 0.5 }}>
                            {t('settings.twoFactor.resendButtonSent')}
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                  
                  {!isLoading2FA && !showVerification && (
                    <Button
                      variant="text"
                      size="small"
                      onClick={() => refresh2FAStatus()}
                      sx={{
                        color: '#FFFFFF',
                        backgroundColor: '#00A3FF',
                        '&:hover': {
                          backgroundColor: '#0082CC',
                          color: '#FFFFFF',
                        },
                      }}
                    >
                      {t('settings.twoFactor.refreshButton')}
                    </Button>
                  )}
                  
                  {isLoading2FA && !showVerification && (
                    <CircularProgress 
                      size={20} 
                      sx={{ 
                        color: '#00A3FF'
                      }} 
                    />
                  )}
                </Box>
                
                <Typography variant="body2" color="textSecondary">
                  {t('settings.twoFactor.description')}
                </Typography>
                
                {/* 2FA Alert */}
                {alert2FA && (
                  <Alert 
                    severity={alert2FA.type} 
                    sx={{ 
                      mt: 2, 
                      '& .MuiAlert-message': { 
                        color: (theme) => {
                          switch (alert2FA.type) {
                            case 'success': return theme.palette.success.dark;
                            case 'error': return theme.palette.error.dark;
                            case 'warning': return theme.palette.warning.dark;
                            case 'info': return theme.palette.info.dark;
                            default: return theme.palette.text.primary;
                          }
                        }
                      } 
                    }}
                    onClose={() => setAlert2FA(null)}
                  >
                    {alert2FA.message}
                  </Alert>
                )}
              </Box>
            )}

            {showVerification && (
              <Box sx={{ mt: 3 }}>
                <TextField
                  fullWidth
                  label={t('settings.twoFactor.codeInputLabel', 'Verification Code')}
                  variant="outlined"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  onBlur={() => {
                    const error = validate2FACode(verificationCode);
                    setVerificationCodeError(error);
                  }}
                  autoComplete="one-time-code"
                  error={!!verificationCodeError}
                  helperText={verificationCodeError}
                  inputProps={{
                    maxLength: 6,
                    inputMode: "numeric"
                  }}
                  sx={{ 
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                      '&:hover fieldset': {
                        borderColor: '#00A3FF',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#00A3FF',
                      },
                    },
                    '& .MuiInputLabel-root.Mui-focused': {
                      color: '#00A3FF',
                    },
                  }}
                  disabled={isLoading2FA}
                />
                <Button
                  variant="contained"
                  onClick={handleVerifyCode}
                  disabled={!verificationCode || isLoading2FA || isRecovering}
                  sx={{
                    bgcolor: '#00A3FF',
                    '&:hover': {
                      bgcolor: '#0082CC',
                    },
                    textTransform: 'none',
                    px: 4,
                    py: 1.5,
                    borderRadius: '8px',
                    boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
                    '&:disabled': {
                      backgroundColor: 'rgba(0, 163, 255, 0.5)',
                    }
                  }}
                >
                  {(isLoading2FA || isRecovering) ? <CircularProgress size={24} /> : t('settings.twoFactor.verifyButton')}
                </Button>
              </Box>
            )}
          </Paper>
          
          {/* Cookie Management Section */}
          <Paper elevation={1} className="bg-gray-800 rounded-lg shadow-md" sx={{ mb: 4, p: 3, bgcolor: 'background.paper', overflowX:'hidden', px: { xs:2, md:3 } }} id="cookie-management">
            <CookieManagementCard />
          </Paper>
          
          <Snackbar
            open={!!successMessage}
            autoHideDuration={6000}
            onClose={() => setSuccessMessage(null)}
            message={successMessage}
            sx={{
              '& .MuiSnackbarContent-root': {
                bgcolor: '#00A3FF',
                borderRadius: '8px',
              },
            }}
          />
        </Box>
      </PageContainer>
    </Box>
  );
}

export default Settings;
