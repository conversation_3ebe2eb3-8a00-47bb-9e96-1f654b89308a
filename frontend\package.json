{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@google-cloud/translate": "^9.0.1", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^5.15.7", "@mui/material": "^5.15.7", "@phosphor-icons/react": "^2.1.7", "@react-oauth/google": "^0.12.1", "@types/d3": "^7.4.3", "axios": "^1.7.9", "d3": "^7.9.0", "date-fns": "^4.1.0", "framer-motion": "^10.17.9", "gridstack": "^12.1.0", "i18next": "^25.0.0", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "jspdf": "^3.0.1", "react": "18.3.1", "react-dom": "18.3.1", "react-draggable": "^4.4.6", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.1.1", "recharts": "^2.15.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "18.3.18", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.3.5", "@types/recharts": "^1.8.29", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5", "vitest": "^1.5.0", "@testing-library/react": "^14.2.1", "@testing-library/jest-dom": "^6.4.4", "@testing-library/user-event": "^14.4.3", "jsdom": "^22.1.0"}, "overrides": {"react": "18.3.1", "react-dom": "18.3.1", "@types/react": "18.3.18", "@types/react-dom": "18.3.5"}}